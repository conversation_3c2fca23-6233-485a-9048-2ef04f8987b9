package com.ejuetc.commons.base.querydomain.impl;

import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.EntityManager;

import java.util.HashMap;
import java.util.Map;

public class SubclassMap {

    private Map<Class, Map<String, Class>> map = new HashMap<>();

    private static SubclassMap instance;

    public static SubclassMap subclassMap(EntityManager entityManager) {
        if (instance == null) synchronized (SubclassMap.class) {
            if (instance == null) instance = new SubclassMap(entityManager);
        }
        return instance;
    }

    public SubclassMap(EntityManager entityManager) {
        entityManager.getMetamodel().getEntities().stream().forEach(entityType -> {
            Class<?> javaType = entityType.getJavaType();
            DiscriminatorValue discriminatorValue = javaType.getAnnotation(DiscriminatorValue.class);
            if (discriminatorValue != null) {
                Class superClass = javaType.getSuperclass();
                putCache(javaType, discriminatorValue.value(), superClass);
            }
        });
    }

    private void putCache(Class<?> entityClass, String discriminatorValue, Class parentClass) {
        Map<String, Class> classMap = map.get(parentClass);
        if (classMap == null) {
            classMap = new HashMap<>();
            map.put(parentClass, classMap);
        }
        classMap.put(discriminatorValue, entityClass);

        Class superclass = parentClass.getSuperclass();
        if (superclass != null && !Object.class.equals(superclass))
            putCache(entityClass, discriminatorValue, superclass);
    }

    public Class get(Class domainClass, String subclassType) {
        return map.get(domainClass).get(subclassType);
    }
}

