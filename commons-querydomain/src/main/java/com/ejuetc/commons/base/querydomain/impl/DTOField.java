package com.ejuetc.commons.base.querydomain.impl;

import com.ejuetc.commons.base.clazz.ClassUtils;
import com.ejuetc.commons.base.clazz.SubtypeCode;
import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import jakarta.persistence.criteria.*;
import lombok.SneakyThrows;

import java.lang.reflect.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.ejuetc.commons.base.clazz.ClassUtils.*;
import static com.ejuetc.commons.base.clazz.SubtypeCodes.subtypeCodes;
import static com.ejuetc.commons.base.collection.CollectionUtils.getValue;
import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;
import static java.util.Arrays.asList;
import static java.util.Arrays.stream;
import static java.util.Collections.emptyList;
import static org.apache.logging.log4j.util.Strings.isBlank;
import static com.ejuetc.commons.base.querydomain.impl.QueryExecuter.*;

public abstract class DTOField {
    protected final QueryField queryFieldAnn;
    protected final Field field;

    protected List<Object> values;

    protected QueryExecuter<?, ?> queryExecuter;


    public static List<Predicate> predicatesByValues(From<?, ?> from, Collection values, String operator, QueryExecuter queryExecuter) {
        return predicatesByValues(from, field2values(values, queryExecuter), operator);
    }

    public static List<Predicate> predicatesByValues(From<?, ?> from, List<DTOField> values, String operator) {
        return values.stream()
                .flatMap(field -> field.makePredicates(from, operator).stream())
                .collect(Collectors.toList());
    }

    protected static List<DTOField> field2values(Collection dtos, QueryExecuter queryExecuter) {
        final Map<DTOField, List<Object>> field2values = new HashMap<>();
        dtos.forEach(dto ->
                field2Value(dto, dto.getClass(), queryExecuter).forEach((field, value) -> {
                            List<Object> values = field2values.get(field);
                            if (values == null) {
                                values = new ArrayList<>();
                                field2values.put(field, values);
                            }
                            values.add(value);
                        }
                )
        );

        field2values.forEach((field, values) -> field.setValues(values));
        return field2values.keySet().stream().toList();
    }

    private static Map<DTOField, Object> field2Value(Object dto, Class aimType, QueryExecuter queryExecuter) {
        Map<DTOField, Object> property2value = new HashMap<>();
        for (Field field : aimType.getDeclaredFields()) {
            QueryField queryField = field.getAnnotation(QueryField.class);
            if (queryField == null || queryField.loadOnly()) continue;

            DTOField dtoField = dtoField(field, queryExecuter);
            Object value = dtoField.getFieldValue(dto);
            if (value == null || "".equals(value))
                continue;

            property2value.put(dtoField, value);
        }

        Class<?> superclass = aimType.getSuperclass();
        if (!superclass.equals(Object.class)) {
            property2value.putAll(field2Value(dto, superclass, queryExecuter));
        }
        return property2value;
    }

    public static DTOField dtoField(Field field, QueryExecuter queryExecuter) {
        Class<?> fieldType = field.getType();
        if (Collection.class.isAssignableFrom(fieldType)) {
            AnnotatedType[] typeArguments = ((AnnotatedParameterizedType) field.getAnnotatedType()).getAnnotatedActualTypeArguments();
            return typeArguments.length > 0 && ((Class) typeArguments[0].getType()).getDeclaredAnnotation(QueryDomain.class) == null ?
                    new BasicField(field, queryExecuter) : new CollectionEntityField(field, queryExecuter);
        }
        if (fieldType.getAnnotation(QueryDomain.class) != null)
            return new EntityDTOField(field, queryExecuter);
        return new BasicField(field, queryExecuter);
    }

    protected DTOField(Field field, QueryExecuter queryExecuter) {
        this.field = field;
        this.queryFieldAnn = field.getAnnotation(QueryField.class);
        this.queryExecuter = queryExecuter;
    }

    public String getName() {
        return queryFieldAnn == null || isBlank(queryFieldAnn.value()) ? field.getName() : queryFieldAnn.value();
    }

    @SneakyThrows
    public Object getFieldValue(Object dto) {
        if (field.getType().isPrimitive())
            throw new RuntimeException("域[" + field + "]类型为基础类型,无法表达null语义,建议使用对应包装类型[Integer,Boolean,...]!");

        try {
            return ClassUtils.getFieldValue(dto, field);
        } catch (IllegalArgumentException e) {//从DTO父类对象获取之类DTO属性值的情况
            return null;
        }
    }

    public Path getPath(From<?, ?> from) {
        return getPath(from, getName());
    }

    public static Path getPath(From<?, ?> from, String fieldPath) {
        String[] fieldPaths = fieldPath.split("\\.");
        Path path = from.get(fieldPaths[0]);
        for (int i = 1; i < fieldPaths.length; i++) {
            path = path.get(fieldPaths[i]);
        }
        return path;
    }

    @Override
    public int hashCode() {
        return field.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof DTOField dtoField) {
            return field.equals(dtoField.field);
        }
        return false;
    }

    public From<?, ?> getChildFromCache(From<?, ?> from) {
        From<?, ?> childFrom = queryExecuter.field2fromMap().get(field);
        if (childFrom == null) {
            childFrom = makeChildFrom(from);
            queryExecuter.field2fromMap().put(this, childFrom);
        }
        return childFrom;
    }

    private void setValues(List<Object> values) {
        this.values = values;
    }

    public abstract List<Predicate> makePredicates(From<?, ?> from, String operator);

    public abstract Object domain2dtoFieldValue(Object domain, Object templateFieldValue);

    protected abstract From<?, ?> makeChildFrom(From<?, ?> from);


    public boolean isEntityType() {
        return false;
    }
}

class CollectionEntityField extends DTOField {
    @Override
    public boolean isEntityType() {
        return true;
    }

    public CollectionEntityField(Field field, QueryExecuter queryExecuter) {
        super(field, queryExecuter);
    }

    @Override
    public List<Predicate> makePredicates(From<?, ?> from, String operator) {
        Collection values = (Collection) this.values.get(0);
        List<DTOField> dtoField2Values = field2values(values, queryExecuter);
        if (dtoField2Values.isEmpty())
            return emptyList();

        if (queryExecuter.queryTerm().isMustDBPaging())
            throw new CodingException("一对多条件查询若需翻页请使用内存翻页(请注意数据量)!");
        else
            collectionFieldFilter(true);

        From<?, ?> childFrom = getChildFromCache(from);
        return predicatesByValues(childFrom, dtoField2Values, operator);
    }

    @Override
    public Object domain2dtoFieldValue(Object domain, Object templateFieldValue) {
        Collection tempCollFieldValue = (Collection) templateFieldValue;

        Collection domainCollFieldValues = (Collection) getPropertyValue(domain, getName());
        if (domainCollFieldValues == null || domainCollFieldValues.isEmpty()) return null;

        Collection<Object> dtoCollFieldInstance = newCollection(tempCollFieldValue.getClass());
        Object tempCollFieldFirstValue = tempCollFieldValue.iterator().next();
        for (Object domainCollFieldValue : domainCollFieldValues) {
            dtoCollFieldInstance.add(convert2DTO(domainCollFieldValue, tempCollFieldFirstValue));
        }
        return dtoCollFieldInstance;
    }

    @Override
    protected From<?, ?> makeChildFrom(From<?, ?> from) {
        return (From<?, ?>) from.fetch(getName(), queryFieldAnn.joinType());
    }
}

class EntityDTOField extends DTOField {
    @Override
    public boolean isEntityType() {
        return true;
    }

    public EntityDTOField(Field field, QueryExecuter queryExecuter) {
        super(field, queryExecuter);
    }

    @Override
    public List<Predicate> makePredicates(From<?, ?> from, String operator) {
        List<DTOField> dtoField2Values = field2values(values, queryExecuter);
        if (dtoField2Values.isEmpty()) return emptyList();

        From<?, ?> childFrom = getChildFromCache(from);
        return predicatesByValues(childFrom, dtoField2Values, operator);
    }

    @Override
    public Object domain2dtoFieldValue(Object domain, Object templateFieldValue) {
        Object associateDomain = getPropertyValue(domain, getName());
        if (associateDomain == null) {
            return null;
        }
        return convert2DTO(associateDomain, templateFieldValue);
    }

    @Override
    protected From<?, ?> makeChildFrom(From<?, ?> from) {
        return from.join(getName(), queryFieldAnn.joinType());
    }
}

class BasicField extends DTOField {

    public BasicField(Field field, QueryExecuter queryExecuter) {
        super(field, queryExecuter);
    }

    @Override
    public List<Predicate> makePredicates(From<?, ?> from, String operator) {
        final Path path = getPath(from);
        List<Object> fieldValues = values;
        Class pathValueType = path.getJavaType();
        if (pathValueType.isEnum()) {
            Map<String, Object> map = stream(pathValueType.getEnumConstants()).collect(Collectors.toMap(e -> e.toString(), e -> e));
            fieldValues = fieldValues.stream().map(v -> map.get(v.toString())).collect(Collectors.toList());
            if (operator.equalsIgnoreCase("like"))
                throw new CodingException("枚举字段[%s]不支持like操作!".formatted(getName()));
        }
        if (subtypeCodes().hasCodes(pathValueType)) {
            fieldValues = fieldValues.stream().map(fieldValue -> subtypeCodes().newSubtypeInstance(pathValueType, (String) fieldValue)).collect(Collectors.toList());
        }
        Predicate predicate = switch (operator.toLowerCase()) {
            case "=" -> queryExecuter.builder().equal(path, getValue(fieldValues, 0));
            case "<>" -> queryExecuter.builder().notEqual(path, getValue(fieldValues, 0));
            case ">" -> queryExecuter.builder().greaterThan(path, (Comparable) getValue(fieldValues, 0));
            case ">=" -> queryExecuter.builder().greaterThanOrEqualTo(path, (Comparable) getValue(fieldValues, 0));
            case "<" -> queryExecuter.builder().lessThan(path, (Comparable) getValue(fieldValues, 0));
            case "<=" -> queryExecuter.builder().lessThanOrEqualTo(path, (Comparable) getValue(fieldValues, 0));
            case "like" -> queryExecuter.builder().like(path, getValue(fieldValues, 0) + "");
            case "between" -> queryExecuter.builder().between(path, (Comparable) getValue(fieldValues, 0), (Comparable) getValue(fieldValues, 1));
            case "in" -> {
                CriteriaBuilder.In<Object> in = queryExecuter.builder().in(path);
                fieldValues.forEach(v -> in.value(v));
                yield in;
            }
            case "not in" -> {
                CriteriaBuilder.In<Object> in = queryExecuter.builder().in(path);
                fieldValues.forEach(v -> in.value(v));
                yield queryExecuter.builder().not(in);
            }
            default -> throw new RuntimeException("未知断言符号[" + operator + "]");
        };

        return asList(predicate);
    }

    @Override
    public Object domain2dtoFieldValue(Object domain, Object templateFieldValue) {
        Object fieldValue = getPropertyValue(domain, getName());
        if (fieldValue == null) return null;

        if (field.getType().equals(String.class)) {
            SubtypeCode subtypeCodeAnno = getClassAnnotation(fieldValue.getClass(), SubtypeCode.class);
            if (subtypeCodeAnno != null)
                return subtypeCodeAnno.code();
            else
                return fieldValue.toString();
        }
        return fieldValue;
    }

    @Override
    protected From<?, ?> makeChildFrom(From<?, ?> from) {
        return from;
    }

}
