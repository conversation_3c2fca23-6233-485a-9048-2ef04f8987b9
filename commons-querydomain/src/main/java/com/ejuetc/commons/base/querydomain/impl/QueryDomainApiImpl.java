/**
 *
 */
package com.ejuetc.commons.base.querydomain.impl;


import com.ejuetc.commons.base.response.Page;
import com.ejuetc.commons.base.querydomain.api.QueryDomainAPI;
import com.ejuetc.commons.base.querydomain.api.QueryResponse;
import com.ejuetc.commons.base.querydomain.api.QueryTerm;
import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.ejuetc.commons.base.querydomain.impl.Domain2DTOConvert.convert2DTO;

@Slf4j
@RestController
@Component
@Transactional(rollbackFor = Exception.class)
public class QueryDomainApiImpl implements QueryDomainAPI {

    private final EntityManager entityManager;

    public QueryDomainApiImpl(EntityManager entityManager) {
        this.entityManager = entityManager;
    }

    @Override
    public <DTO> QueryResponse<DTO> query(QueryTerm<DTO> queryTerm) {
        try {
            return doFind(queryTerm);
        } catch (Throwable t) {
            log.error("查询[" + queryTerm + "]发生异常:", t);
            return new QueryResponse(t);
        }
    }


    private <DTO> QueryResponse<DTO> doFind(QueryTerm<DTO> queryTerm) {
        QueryExecuter executer = new QueryExecuter(queryTerm, entityManager);
        List domains = executer.query();
        List result = domains;

        Page page = queryTerm.getPage();
        if (queryTerm.isNeedPaging()) {
            Long count = null;
            if (executer.isMemoryPaging()) {
                count = (long) domains.size();
                result = page.subList(domains);
            } else {
                count = new QueryExecuter(queryTerm, entityManager).count();
            }
            page.setTotalRow(count);
        }

        List dtos = Domain2DTOConvert.convert2DTO(result, queryTerm.getLoadTemplate());
        QueryResponse queryResponse = new QueryResponse(dtos);
        queryResponse.setPage(page);
        return queryResponse;
    }

}
