package com.ejuetc.commons.base.querydomain.impl;

import com.ejuetc.commons.base.response.Page;
import com.ejuetc.commons.base.querydomain.api.QueryTerm;
import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.*;

public class QueryExecuter<Domain, DTO> implements Cloneable {
    @Getter
    @Accessors(fluent = true)
    private Map<DTOField, From> field2fromMap;
    @Getter
    @Accessors(fluent = true)
    private CriteriaBuilder builder;

    @Getter
    @Accessors(fluent = true)
    private QueryTerm<?> queryTerm;

    @Getter
    @Setter
    @Accessors(fluent = true)
    private static boolean collectionFieldFilter = false;//是否存在子列表(1:N)属性过滤

    private final EntityManager entityManager;

    public QueryExecuter(QueryTerm<DTO> queryTerm, EntityManager entityManager) {
        this.entityManager = entityManager;

        this.queryTerm = queryTerm;
        this.builder = entityManager.getCriteriaBuilder();
        this.field2fromMap = new HashMap<>();
    }

    public List<Domain> query() {
        Class resultClass = queryTerm.gatherLoadClass();
        CriteriaQuery<Domain> query = builder.createQuery(resultClass);
        Root rootFrom = query.from(resultClass);

        query.select(rootFrom);
        query.distinct(queryTerm.isDistinct());

        Predicate wheres = makePredicate(rootFrom);
        if (wheres != null) query.where(wheres);

        List<Order> orderBy = makeOrders(rootFrom);
        if (!orderBy.isEmpty()) query.orderBy(orderBy);

        TypedQuery<Domain> typedQuery = entityManager.createQuery(query);
        Page page = queryTerm.getPage();
        if (isNeedDBPaging()) {
            typedQuery.setFirstResult(page.calcFirstRowNumber());
            typedQuery.setMaxResults(page.getPageSize());
        }
        return typedQuery.getResultList();
    }

    public Long count() {
        CriteriaQuery<Long> query = builder.createQuery(Long.class);
        Root rootFrom = query.from(queryTerm.getherFromClass());
        if (queryTerm.isDistinct())
            query.select(builder.countDistinct(rootFrom));
        else
            query.select(builder.count(rootFrom));

        List<Order> orderBy = makeOrders(rootFrom);
        if (!orderBy.isEmpty()) query.orderBy(orderBy);

        Predicate wheres = makePredicate(rootFrom);
        if (wheres != null) query.where(wheres);
        return entityManager.createQuery(query).getSingleResult();
    }

    private boolean isNeedDBPaging() {
        return queryTerm.isNeedPaging() && !collectionFieldFilter;
    }

    private List<Order> makeOrders(Root<Domain> rootFrom) {
        List<Order> orders = new ArrayList<>();
        queryTerm.getOrderBy().forEach((fieldPath, asc) -> {
            Path<Object> expression = DTOField.getPath(rootFrom, fieldPath);
            orders.add(asc ? builder.asc(expression) : builder.desc(expression));
        });
        return orders;
    }

    private Predicate makePredicate(From<?, ?> rootFrom) {
        Predicate predicate = null;
        for (QueryTerm.Term term : queryTerm.getTerms()) {
            Predicate newPredicate = makePredicate(term, rootFrom);
            if (predicate == null) {
                predicate = newPredicate;
            } else if (term.getTermsBeAnd()) {
                predicate = builder.and(predicate, newPredicate);
            } else {
                predicate = builder.or(predicate, newPredicate);
            }
        }
        return predicate;
    }

    private Predicate makePredicate(QueryTerm.Term term, From<?, ?> rootFrom) {
        Collection fromValues = term.getDtos();
        String operator = term.getOperator();
        List<Predicate> predicates = DTOField.predicatesByValues(rootFrom, fromValues, operator, this);

        if (predicates.size() == 1) {
            return predicates.get(0);
        } else if (term.getFieldBeAnd()) {
            return builder.and(predicates.toArray(new Predicate[predicates.size()]));
        } else {
            return builder.or(predicates.toArray(new Predicate[predicates.size()]));
        }
    }


    public boolean isMemoryPaging() {
        return queryTerm.isCanMemoryPaging() && collectionFieldFilter;
    }
}

