package com.ejuetc.commons.base.querydomain.impl;

import com.ejuetc.commons.base.clazz.ClasspathFileLoad;
import com.ejuetc.commons.base.querydomain.api.QueryDomain;
import com.ejuetc.commons.base.querydomain.api.QueryField;
import lombok.Getter;
import org.hibernate.Hibernate;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.ejuetc.commons.base.clazz.ClassUtils.newInstance;
import static com.ejuetc.commons.base.clazz.ClassUtils.setObjectValueToField;
import static com.ejuetc.commons.base.querydomain.impl.DTOField.dtoField;


public class Domain2DTOConvert {

    @Getter(lazy = true)
    private final static Map<String, ? extends Class<?>> domain2dtoMap = domain2dtoMap();

    private static Map<String, ? extends Class<?>> domain2dtoMap() {
        Set<Class<?>> classes = new ClasspathFileLoad().findAnnoByWorkPackage(QueryDomain.class);
        return classes.stream().collect(Collectors.toMap(c -> c.getAnnotation(QueryDomain.class).value(), c -> c));
    }

    private static Class getDTOClass(Object domain) {
        Class<?> domainClass = Hibernate.getClass(domain);
        do {
            Class<?> dtoClass = getDomain2dtoMap().get(domainClass.getName());
            if (dtoClass != null) return dtoClass;
            domainClass = domainClass.getSuperclass();
        } while (domainClass != Object.class);
        throw new RuntimeException("无法找到类型[ " + Hibernate.getClass(domain).getName() + "]对应的DTO类型");
    }

    public static <Domain, DTO> List<DTO> convert2DTO(List<Domain> domains, DTO dtoTemplate) {
        return domains.stream().map(domain -> convert2DTO(domain, dtoTemplate)).collect(Collectors.toList());
    }

    public static <Domain, DTO> DTO convert2DTO(Domain domain, DTO dtoTemplate) {
        Class<?> dtoClass = dtoTemplate.getClass();//getDTOClass(domain);
        Object dtoInstance = newInstance(dtoClass);
        copyFieldValues(domain, dtoTemplate, dtoClass, dtoInstance);
        return (DTO) dtoInstance;
    }

    private static <DTO> void copyFieldValues(Object domain, DTO dtoTemplate, Class<?> aimClass, DTO aimDTO) {
        for (Field field : aimClass.getDeclaredFields()) {
            QueryField queryField = field.getAnnotation(QueryField.class);
            if (queryField == null) continue;

            DTOField dtoField = dtoField(field, null);
            Object tempFieldValue = dtoField.getFieldValue(dtoTemplate);
            boolean isLoad = queryField.loadPolicy().isLoad(dtoField.isEntityType(), tempFieldValue);
            if (!isLoad) continue;

            Object dtoFieldValue = dtoField.domain2dtoFieldValue(domain, tempFieldValue);
            if (dtoFieldValue != null) {
                setObjectValueToField(aimDTO, field, dtoFieldValue);
            }
        }

        Class<?> superclass = aimClass.getSuperclass();
        if (!superclass.equals(Object.class))
            copyFieldValues(domain, dtoTemplate, superclass, aimDTO);
    }


}
