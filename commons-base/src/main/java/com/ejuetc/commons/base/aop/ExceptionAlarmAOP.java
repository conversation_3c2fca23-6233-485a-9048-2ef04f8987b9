package com.ejuetc.commons.base.aop;

import com.ejuetc.commons.base.callback.MessageCallback;
import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.spring.SpringUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalCause;
import com.github.benmanes.caffeine.cache.RemovalListener;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.ejuetc.commons.base.exception.ThrowableUtils.getRootCause;
import static com.ejuetc.commons.base.exception.ThrowableUtils.throwStackTrace;
import static com.ejuetc.commons.base.filter.LoggerFilter.HTTP_REQUEST_LOG;
import static com.ejuetc.commons.base.spring.SpringUtil.getBean;

@Aspect
@Component
@NoArgsConstructor
@Slf4j
public class ExceptionAlarmAOP implements RemovalListener<String, Map<String, Integer>> {

    @Value("${com.ejuetc.exception.alarm:false}")
    private boolean onOff;

    private final Cache<String, Map<String, Integer>> cache = Caffeine.newBuilder()
            .initialCapacity(5)//初始数量
            .maximumSize(1000)//最大条数
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .removalListener(this)
            .build();

    @AfterThrowing(pointcut = "execution(* com.ejuetc..*Service*.*(..)) || execution(* com.ejuetc..*Filter*.*(..)) || execution(* com.ejuetc..*API*.*(..)) || execution(* com.ejuetc..*Api*.*(..)) || execution(* com.ejuetc..*Controller*.*(..))", throwing = "e")
    public void exceptionAlarm(JoinPoint jp, Throwable e) {
        Class declaringType = jp.getSignature().getDeclaringType();
        String jpSign = jp.getSignature().toString();

        exceptionAlarm(declaringType, jpSign, e);
    }

    public void exceptionAlarm(Class declaringType, String jpSign, Throwable e) {
        MessageCallback messageCB = getBean(MessageCallback.class);
        if (messageCB == null) {
            log.warn("无消息回调接口[" + MessageCallback.class + "]实例,因此无法发送异常报警!");
            return;
        }

        if (!onOff || e instanceof BusinessException || declaringType.equals(MessageCallback.class)) {
            return;
        }

        Throwable rootCause = getRootCause(e);
        String causeInfo = rootCause.toString();
        Map<String, Integer> jpCountMap = cache.get(causeInfo, k -> new HashMap<>());
        if (jpCountMap.isEmpty()) {
            cache.put(causeInfo, jpCountMap);

            Map<String, Object> params = new HashMap<>();
            params.put("environment", SpringUtil.getProperty("spring.profiles.active"));
            params.put("appName", SpringUtil.getProperty("spring.application.name"));
            params.put("joinPointSign", jpSign);
            params.put("rootCauseMsg", rootCause.getMessage());
            params.put("throwStackTrace", throwStackTrace(e));
            params.put("httpRequestLog", HTTP_REQUEST_LOG.get());
            try {
                messageCB.sendMessage("exceptionAlarm", params, false);
            } catch (Throwable t) {
                log.error("发送报警发送异常:", t);
            }
        }

        Integer count = jpCountMap.computeIfAbsent(jpSign, k -> 0);
        jpCountMap.put(jpSign, count + 1);
    }

    @Override
    public void onRemoval(@Nullable String rootCause, @Nullable Map<String, Integer> jpSignCount, RemovalCause removalCause) {
        MessageCallback messageCB = getBean(MessageCallback.class);
        if (jpSignCount == null || jpSignCount.isEmpty() || messageCB == null) return;
        Iterator<Map.Entry<String, Integer>> iterator = jpSignCount.entrySet().iterator();
        if (iterator.next().getValue() == 1 && !iterator.hasNext()) return;

        StringBuffer statistics = new StringBuffer();
        jpSignCount.forEach((jpSign, count) -> {
            statistics.append("方法[").append(jpSign).append("]异常次数[").append(count).append("]\r\n");
        });

        Map<String, Object> params = new HashMap<>();
        params.put("environment", SpringUtil.getProperty("spring.profiles.active"));
        params.put("appName", SpringUtil.getProperty("spring.application.name"));
        params.put("rootCauseMsg", rootCause);
        params.put("removalCause", removalCause.toString());
        params.put("statistics", statistics);
        messageCB.sendMessage("exceptionStatistics", params, false);
    }
}
