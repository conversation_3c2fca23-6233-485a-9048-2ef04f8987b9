package com.ejuetc.commons.base.querydomain.api;

import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.exception.Error;
import com.ejuetc.commons.base.response.ApiResponse;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.experimental.Accessors;

import java.util.List;

import static com.ejuetc.commons.base.exception.ThrowableUtils.throwStackTrace;
import static com.ejuetc.commons.base.response.ResponseStatus.FAIL_SYS;
import static com.ejuetc.commons.base.response.ResponseStatus.SUCC_DONE;

@Accessors(chain = true)
public class QueryResponse<DTO> extends ApiResponse<List<DTO>> {

    public QueryResponse() {
        super(SUCC_DONE, SUCC_DONE.getMessage(), null);
    }

    public QueryResponse(List<DTO> data) {
        super(SUCC_DONE, SUCC_DONE.getMessage(), data);
        this.data = data;
    }

    @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
    private List<DTO> data;

    public QueryResponse(Throwable t) {
        super(FAIL_SYS, t.getMessage(), null);
        setError(new Error(t.getClass().getSimpleName(), null, throwStackTrace(t)));
    }

    @JsonIgnore
    public DTO getDTO() {
        if (data == null || data.isEmpty())
            return null;
        if (data.size() > 1)
            throw new CodingException("查询到多个值,请确认查询条件是否正确!");
        return data.get(0);
    }

    public List<DTO> getData() {
        return data;
    }

    @Override
    public ApiResponse setData(List<DTO> data) {
        this.data = data;
        return this;
    }
}
