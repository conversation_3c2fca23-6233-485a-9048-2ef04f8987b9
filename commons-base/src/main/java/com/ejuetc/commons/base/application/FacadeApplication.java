package com.ejuetc.commons.base.application;

import com.ejuetc.commons.base.filter.ClearJsonTypeFilter;
import jakarta.servlet.Filter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;

import static com.ejuetc.commons.base.filter.BaseJsonFilter.BASE_FILTER_ORDER;
import static java.util.Arrays.asList;

public class FacadeApplication extends BaseApplication {

    @Bean
    public FilterRegistrationBean filters() {
        FilterRegistrationBean<Filter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new ClearJsonTypeFilter());   //设置过滤器
        registrationBean.setUrlPatterns(asList("/*"));
        registrationBean.setOrder(BASE_FILTER_ORDER + 2);  //设置优先级
        return registrationBean;
    }

}
