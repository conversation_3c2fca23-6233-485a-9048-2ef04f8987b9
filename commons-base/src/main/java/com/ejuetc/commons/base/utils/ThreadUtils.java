package com.ejuetc.commons.base.utils;

import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.valueobj.Command;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.support.TransactionSynchronization;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.*;
import java.util.HashSet;

import static com.ejuetc.commons.base.spring.SpringUtil.getBean;
import static com.ejuetc.commons.base.utils.StringUtils.makeString;
import static java.lang.Math.min;
import static java.lang.Thread.sleep;
import static java.time.Duration.ofSeconds;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.codec.digest.DigestUtils.md5Hex;
import static org.springframework.transaction.support.TransactionSynchronizationManager.registerSynchronization;

@Slf4j
public class ThreadUtils {

    private static final ExecutorService executor = new ThreadPoolExecutor(
            50,
            200,
            5L,
            TimeUnit.MINUTES,
            new SynchronousQueue<>(),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    static {
        Runtime.getRuntime().addShutdownHook(new Thread(ThreadUtils::shutdown));
    }

    public static void asyncExec(Command command) {
        asyncExec(command, 0);
    }

    public static void asyncExec(Command command, long delay) {
        executor.submit(() -> {
            try {
                if (delay > 0) sleep(delay);
                command.run();
            } catch (InterruptedException e) {
                log.error("runnable error", e);
            }
        });
    }

    @SneakyThrows
    public static <T> List<T> invokeAll(Collection<? extends Callable<T>> list) {
        List<Future<T>> futures = executor.invokeAll(list);
        return futures.stream().map(f -> {
            try {
                return f.get();
            } catch (Exception e) {
                log.error("runnable error", e);
                return null;
            }
        }).collect(toList());
    }

    @SneakyThrows
    public static <T> List<Future<T>> invokeAllReturnFuture(Collection<? extends Callable<T>> list) {
        return executor.invokeAll(list);
    }

    private static void shutdown() {
        executor.shutdown();
        try {
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    private static final ThreadLocal<Set<String>> threadLocalLocks = ThreadLocal.withInitial(HashSet::new);

    public static void redisLock(long waitSeconds, long lockSeconds, Object... keys) {
        String keySource = makeString("_", keys);
        String key = md5Hex(keySource);
        log.info("redisLock:keySource={} , key{}", keySource, key);

        Set<String> currentLocks = threadLocalLocks.get();
        if (currentLocks.contains(key)) {
            log.info("redisLock already acquired:keySource={} , key={}", keySource, key);
            return;
        }

        long waitTimeMs = ofSeconds(waitSeconds).toMillis();
        long start = System.currentTimeMillis();
        while (true) {
            Boolean set = getBean(StringRedisTemplate.class).opsForValue().setIfAbsent(key, "1", ofSeconds(lockSeconds));
            if (Objects.equals(true, set)) {
                currentLocks.add(key);
                registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCompletion(int status) {
                        getBean(StringRedisTemplate.class).delete(key);
                        currentLocks.remove(key);
                        log.info("redisLock release:keySource={} , key{}", keySource, key);
                    }
                });
                log.info("redisLock succ:keySource={} , key{}", keySource, key);
                return;
            }
            if (System.currentTimeMillis() - start > waitTimeMs) {
                throw new CodingException("获取分布式锁[%s]超时", keys);
            }
            try {
                sleep(50);
            } catch (InterruptedException e) {
                throw new CodingException("获取分布式锁异常:", e);
            }
        }
    }


}
