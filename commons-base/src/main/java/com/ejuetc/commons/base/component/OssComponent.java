package com.ejuetc.commons.base.component;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.*;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.*;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.ejuetc.commons.base.exception.CodingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.response.ApiResponse.succ;
import static com.ejuetc.commons.base.utils.DateTimeUtils.formatDate;
import static com.ejuetc.commons.base.utils.StringUtils.notBlank;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.UUID.randomUUID;

@Slf4j
@Data
@Accessors(chain = true)
@Component
@ConfigurationProperties(prefix = "ejuetc.commons.oss")
@ConditionalOnProperty(name = "ejuetc.commons.oss.access-key-secret")
public class OssComponent implements DisposableBean {

    private String ossEndpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String urlPrefix;
    private String bucketName;
    private Map<String, Function> uploadFunctions;
    private String roleArnForOssUpload = "acs:ram::1223625809225662:role/client-upload-oss";    //环境变量中获取ARN
    private String regionId = "cn-shanghai";    //发起STS请求所在的地域
    private String roleSessionName = "client-upload-oss-session";    //角色会话名称，用来区分不同的令牌，可自定义


    @Getter(lazy = true)
    @Accessors(fluent = true)
    private final OSS ossClient = makeOssClient();

    public String putOSS(String ossFilePath, InputStream inputStream) {
        return putOSS(ossFilePath, inputStream, null);
    }

    public String urlConvert(String sourceUrl, String ossFilePath) {
        try (InputStream inputStream = new URL(sourceUrl).openStream()) {
            URLConnection connection = new URL(sourceUrl).openConnection();
            String contentType = connection.getContentType();
            return putOSS(ossFilePath, inputStream, contentType);
        } catch (IOException e) {
            throw new RuntimeException("Failed to convert URL to OSS [" + sourceUrl + "]", e);
        }
    }

    public String putOSS(String ossFilePath, InputStream inputStream, String contentType) {
        try {
            ObjectMetadata objectMetadata = null;
            if (notBlank(contentType)) {
                objectMetadata = new ObjectMetadata();
                objectMetadata.setContentType(contentType);
            }
            ossClient().putObject(bucketName, ossFilePath, inputStream, objectMetadata);
            return urlPrefix + (urlPrefix.endsWith("/") ? "" : "/") + ossFilePath;
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException ignored) {
                }
            }
        }
    }

    public Map<String, String> makePostFileForm(Long userId, String function, boolean postOrSign) {
        Function config = uploadFunctions.get(function);
        if (config == null) throw new CodingException("功能[%s]不能上传文件".formatted(function));

        String parentDir = "%s/%s/%s/".formatted(
                function,
                userId,
                formatDate(new Date(), config.getDateFormat())
        );
        int existCount = countObjects(parentDir);
        if (existCount >= config.getMaxCount()) throw new CodingException("超出最大文件上传数!");

        String fileDir = parentDir + randomUUID();
        Map<String, String> map = postOrSign
                ? makePostFileForm(fileDir, config.getValidSeconds(), config.maxFileSize)
                : makeClientUploadSignature(fileDir, config.getValidSeconds());
        HashMap<String, String> result = new HashMap<>(map);
        result.put("dir", fileDir);
        result.put("host", getHost());
        result.put("urlPrefix", urlPrefix);
        return result;
    }

    @SneakyThrows
    public Map<String, String> makePostFileForm(String fileDir, long validSeconds, int maxFileSize) {
        Date expiration = new Date(System.currentTimeMillis() + validSeconds * 1000);
        PolicyConditions policyConditions = new PolicyConditions();
        policyConditions.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, maxFileSize);
        policyConditions.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, fileDir);
        String postPolicy = ossClient().generatePostPolicy(expiration, policyConditions);
        String encodedPolicy = BinaryUtil.toBase64String(postPolicy.getBytes(UTF_8));
        String postSignature = ossClient().calculatePostSignature(postPolicy);

        return Map.of(
                "ossAccessKeyId", accessKeyId,
                "policy", encodedPolicy,
                "signature", postSignature
        );
    }

    private String getHost() {
        return "https://" + bucketName + "." + ossEndpoint;
    }

    private OSS makeOssClient() {
        return new OSSClientBuilder().build(ossEndpoint, accessKeyId, accessKeySecret);
    }

    public int countObjects(String prefix) {
        int keyCount = 0;
        ListObjectsV2Result result = ossClient().listObjectsV2(
                new ListObjectsV2Request(bucketName).withPrefix(prefix).withDelimiter("/")
        );
        keyCount += result.getObjectSummaries().size();
        for (String commonPrefix : result.getCommonPrefixes())
            keyCount += countObjects(commonPrefix); // 递归统计子目录下的文件数
        return keyCount;
    }


    @Override
    public void destroy() {
        ossClient().shutdown();
    }

    @Data
    public static class Function {
        private String dateFormat;
        private int maxCount;
        private long validSeconds = 5 * 60;//    默认:有效期5分钟
        private int maxFileSize = 1024 * 1024 * 10;//   默认:文件最大10M
    }

    @SneakyThrows
    public Map<String, String> makeClientUploadSignature(String fileDir, long validSeconds) {
        AssumeRoleRequest request = new AssumeRoleRequest();
        request.setRoleArn(roleArnForOssUpload);
        request.setRoleSessionName(roleSessionName);
        request.setDurationSeconds(validSeconds);

        //初始化客户端
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);
        AssumeRoleResponse response = client.getAcsResponse(request);

        //将请求返回的STS临时访问凭证赋值到自定义变量中
        String stsAccessKeyId = response.getCredentials().getAccessKeyId();
        String stsSecretAccessKey = response.getCredentials().getAccessKeySecret();
        String securityToken = response.getCredentials().getSecurityToken();


        //格式化请求日期
        long now = System.currentTimeMillis() / 1000;
        ZonedDateTime dtObj = ZonedDateTime.ofInstant(Instant.ofEpochSecond(now), ZoneId.of("UTC"));
        ZonedDateTime dtObjPlus3h = dtObj.plusHours(3);
        //请求时间
        DateTimeFormatter dtObj1Formatter = DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'");
        String dtObj1 = dtObj.format(dtObj1Formatter);
        //请求日期
        DateTimeFormatter dtObj2Formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String dtObj2 = dtObj.format(dtObj2Formatter);
        //请求过期时间
        DateTimeFormatter expirationTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        String expirationTime = dtObjPlus3h.format(expirationTimeFormatter);

        // 创建policy
        // 示例policy表单域只列举必填字段，如有其他需求可参考文档：https://help.aliyun.com/zh/oss/developer-reference/signature-version-4-recommend
        ObjectMapper mapper = new ObjectMapper();

        Map<String, Object> policy = new HashMap<>();
        policy.put("expiration", expirationTime);

        List<Object> conditions = new ArrayList<>();

        Map<String, String> bucketCondition = new HashMap<>();
        bucketCondition.put("bucket", bucketName);  //请将<bucketname>替换为您的实际Bucket名称
        conditions.add(bucketCondition);
        conditions.add(List.of("starts-with", "$key", fileDir));//限定操作目录

        Map<String, String> signatureVersionCondition = new HashMap<>();
        signatureVersionCondition.put("x-oss-signature-version", "OSS4-HMAC-SHA256");
        conditions.add(signatureVersionCondition);

        Map<String, String> credentialCondition = new HashMap<>();
        credentialCondition.put("x-oss-credential", stsAccessKeyId + "/" + dtObj2 + "/" + regionId + "/oss/aliyun_v4_request");     //请将<cn-hangzhou>替换为您的实际Bucket所处地域，例如北京地域为：cn-beijing
        conditions.add(credentialCondition);

        Map<String, String> token = new HashMap<>();
        token.put("x-oss-security-token", securityToken);
        conditions.add(token);

        Map<String, String> dateCondition = new HashMap<>();
        dateCondition.put("x-oss-date", dtObj1);
        conditions.add(dateCondition);

        policy.put("conditions", conditions);
        String jsonPolicy = mapper.writeValueAsString(policy);
        log.info("policy:{}", jsonPolicy);
        //构造待签名字符串（StringToSign）
        String stringToSign = new String(Base64.encodeBase64(jsonPolicy.getBytes()));

        //计算SigningKey
        byte[] dateKey = hmacsha256(("aliyun_v4" + stsSecretAccessKey).getBytes(), dtObj2);
        byte[] dateRegionKey = hmacsha256(dateKey, regionId);    //请将<cn-hangzhou>替换为您的实际Bucket所处地域，例如北京地域为：cn-beijing
        byte[] dateRegionServiceKey = hmacsha256(dateRegionKey, "oss");
        byte[] signingKey = hmacsha256(dateRegionServiceKey, "aliyun_v4_request");

        //计算Signature
        byte[] result = hmacsha256(signingKey, stringToSign);
        String signature = BinaryUtil.toHex(result);

        Map<String, String> messageMap = new HashMap<>();
        messageMap.put("security_token", securityToken);
        messageMap.put("signature", signature);
        messageMap.put("x_oss_date", dtObj1);
        messageMap.put("x_oss_credential", stsAccessKeyId + "/" + dtObj2 + "/" + regionId + "/oss/aliyun_v4_request");    //请将<cn-hangzhou>替换为您的实际Bucket所处地域，例如北京地域为：cn-beijing
        messageMap.put("x_oss_signature_version", "OSS4-HMAC-SHA256");
        messageMap.put("policy", stringToSign);
        return messageMap;
    }

    /**
     * 使用HMAC-SHA256算法计算给定密钥和数据的哈希值的静态方法
     *
     * @param key
     * @param data
     * @return
     */
    public static byte[] hmacsha256(byte[] key, String data) {
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(key, "HmacSHA256");
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(secretKeySpec);
            byte[] hmacBytes = mac.doFinal(data.getBytes());
            return hmacBytes;
        } catch (Exception e) {
            throw new RuntimeException("Failed to calculate HMAC-SHA256", e);
        }
    }

    public static void main(String[] args) {
        OssComponent oss = new OssComponent()
                .setOssEndpoint("oss-cn-shanghai.aliyuncs.com")
                .setAccessKeyId("LTAI5tR7VRT3oCrM2GLe35ck")
                .setAccessKeySecret("******************************")
                .setUrlPrefix("https://etc-consumer.oss-cn-shanghai.aliyuncs.com/")
                .setBucketName("etc-consumer")
                .setUploadFunctions(Map.of(
                        "delegation-media", new Function()
                                .setDateFormat("yyyyMMddHHmm")
                                .setMaxCount(1000)
                                .setValidSeconds(900)
                ));
        Map<String, String> result = oss.makePostFileForm(6612722971394985472L, "delegation-media", false);
        System.out.println(JSON.toJSONString(result, true));
    }

}
