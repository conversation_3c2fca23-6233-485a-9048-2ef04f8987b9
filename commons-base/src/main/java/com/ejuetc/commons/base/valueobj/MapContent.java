package com.ejuetc.commons.base.valueobj;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ejuetc.commons.base.utils.DateTimeUtils.parseDate;
import static com.ejuetc.commons.base.utils.StringUtils.map2string;
import static java.util.stream.Collectors.toMap;

/**
 * Created by lindongcheng on 16/4/30.
 */
public class MapContent implements Map<Object, Object>, Serializable {

    protected Map<Object, Object> content;

    public MapContent(boolean ordered) {
        if (ordered)
            content = new LinkedHashMap<>();
        else
            content = new HashMap<>();
    }

    public MapContent() {
        this(false);
    }

    public MapContent(Map content) {
        this(content instanceof LinkedHashMap<?, ?>);
        this.content.putAll(content);
    }

    public MapContent merge(MapContent merge) {
        Map newContent = new HashMap();
        newContent.putAll(this.content);
        newContent.putAll(merge.content);
        return new MapContent(newContent);
    }

    public String getString(String key) {
        Object value = content.get(key);
        return value != null ? value.toString() : null;
    }

    public String getString(String key, String def) {
        Object value = content.get(key);
        return value != null ? value.toString() : def;
    }

    public Long getLong(String key) {
        return getLong(key, null);
    }

    public Long getLong(String key, Long def) {
        Object value = content.get(key);
        return value == null ? def : value instanceof Long ? (Long) value : Long.valueOf(value.toString());
    }

    public Integer getInteger(String key) {
        return getInteger(key, null);
    }

    public Integer getInteger(String key, Integer def) {
        Object value = content.get(key);
        return value == null ? def : value instanceof Integer ? (Integer) value : Integer.valueOf(value.toString());
    }

    public BigDecimal getBigDecimal(String key) {
        return getBigDecimal(key, null);
    }

    public BigDecimal getBigDecimal(String key, BigDecimal def) {
        Object value = content.get(key);
        return value == null ? def : value instanceof BigDecimal ? (BigDecimal) value : new BigDecimal(value.toString());
    }

    public Map<Object, Object> getContent() {
        return Collections.unmodifiableMap(content);
    }

    public Date getDate(String key) {
        return getDate(key, null);
    }

    public Date getDate(String key, Date def) {
        Object value = content.get(key);
        if (value == null) return def;
        if (value instanceof Date) return (Date) value;
        if (value instanceof Long) return new Date((Long) value);
        return parseDate(value.toString());
    }

    public Map find(String keyPrefix) {
        return (Map) content.keySet().stream()
                .filter(key -> key.toString().startsWith(keyPrefix))
                .collect(toMap(key -> key, key -> content.get(key)));
    }

    @Override
    public int size() {
        return content.size();
    }

    @Override
    public boolean isEmpty() {
        return content.isEmpty();
    }

    @Override
    public boolean containsKey(Object key) {
        return content.containsKey(key);
    }

    @Override
    public boolean containsValue(Object value) {
        return content.containsValue(value);
    }

    @Override
    public Object get(Object key) {
        return content.get(key);
    }

    @Override
    public Object put(Object key, Object value) {
        return content.put(key, value);
    }

    @Override
    public Object remove(Object key) {
        return content.remove(key);
    }

    @Override
    public void putAll(Map<? extends Object, ? extends Object> m) {
        content.putAll(m);
    }

    @Override
    public void clear() {
        content.clear();
    }

    @Override
    public Set<Object> keySet() {
        return content.keySet();
    }

    @Override
    public Collection<Object> values() {
        return content.values();
    }

    @Override
    public Set<Entry<Object, Object>> entrySet() {
        return content.entrySet();
    }

    @Override
    public boolean equals(Object o) {
        return content.equals(o);
    }

    @Override
    public int hashCode() {
        return content.hashCode();
    }

    @Override
    public Object getOrDefault(Object key, Object defaultValue) {
        return content.getOrDefault(key, defaultValue);
    }

    @Override
    public void forEach(BiConsumer<? super Object, ? super Object> action) {
        content.forEach(action);
    }

    @Override
    public void replaceAll(BiFunction<? super Object, ? super Object, ? extends Object> function) {
        content.replaceAll(function);
    }

    @Override
    public Object putIfAbsent(Object key, Object value) {
        return content.putIfAbsent(key, value);
    }

    @Override
    public boolean remove(Object key, Object value) {
        return content.remove(key, value);
    }

    @Override
    public boolean replace(Object key, Object oldValue, Object newValue) {
        return content.replace(key, oldValue, newValue);
    }

    @Override
    public Object replace(Object key, Object value) {
        return content.replace(key, value);
    }

    @Override
    public Object computeIfAbsent(Object key, Function<? super Object, ? extends Object> mappingFunction) {
        return content.computeIfAbsent(key, mappingFunction);
    }

    @Override
    public Object computeIfPresent(Object key, BiFunction<? super Object, ? super Object, ? extends Object> remappingFunction) {
        return content.computeIfPresent(key, remappingFunction);
    }

    @Override
    public Object compute(Object key, BiFunction<? super Object, ? super Object, ? extends Object> remappingFunction) {
        return content.compute(key, remappingFunction);
    }

    @Override
    public Object merge(Object key, Object value, BiFunction<? super Object, ? super Object, ? extends Object> remappingFunction) {
        return content.merge(key, value, remappingFunction);
    }
}
