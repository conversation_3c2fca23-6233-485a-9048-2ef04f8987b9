package com.ejuetc.commons.base.querydomain.api;

import com.ejuetc.commons.base.response.Page;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.*;
import lombok.experimental.Accessors;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static com.ejuetc.commons.base.clazz.SubtypeCodes.subtypeCodes;
import static com.ejuetc.commons.base.utils.StringUtils.isBlank;
import static java.lang.Class.forName;
import static java.util.Arrays.asList;
import static java.util.stream.Collectors.toList;

@Data
@Accessors(chain = true)
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@NoArgsConstructor
public class QueryTerm<DTO> {
    @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
    private DTO loadTemplate;
    private List<Term> terms = new ArrayList<>();
    private Page page;
    @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
    private Map<String, Boolean> orderBy = new LinkedHashMap<>();

    @Getter
    private boolean memoryPaging = false;//内存翻页(仅仅针对条件包含1:N且数据量不大的情况)
    @Getter
    @Setter
    private boolean distinct = false;//对query和count结果进行去重;

    public QueryTerm(DTO loadTemplate) {
        this.loadTemplate = loadTemplate;
    }

    public final QueryTerm<DTO> beginFieldsAnd(String operator, DTO... dtos) {
        return beginFieldsAnd(operator, convert2List(dtos));
    }

    private Collection<DTO> convert2List(DTO... dtos) {
        if (dtos != null && dtos.length == 1 && dtos[0] instanceof Collection)
            return (Collection<DTO>) dtos[0];
        else
            return asList(dtos);
    }

    public QueryTerm<DTO> beginFieldsAnd(String operator, Collection<DTO> dtos) {
        if (terms.size() > 0) throw new RuntimeException("只有添加第一个断言可用该方法!");
        return predicate(null, "and", operator, dtos);
    }

    public QueryTerm<DTO> beginFieldsOr(String operator, DTO... dtos) {
        return beginFieldsOr(operator, convert2List(dtos));
    }

    public QueryTerm<DTO> beginFieldsOr(String operator, Collection<DTO> dtos) {
        if (terms.size() > 0) throw new RuntimeException("只有添加第一个断言可用该方法!");
        return predicate(null, "or", operator, dtos);
    }

    public QueryTerm<DTO> andFieldsAnd(String operator, DTO... dtos) {
        return andFieldsAnd(operator, convert2List(dtos));
    }

    public QueryTerm<DTO> andFieldsAnd(String operator, Collection<DTO> dtos) {
        return predicate("and", "and", operator, dtos);
    }

    public QueryTerm<DTO> andFieldsOr(String operator, DTO... dtos) {
        return andFieldsOr(operator, convert2List(dtos));
    }

    public QueryTerm<DTO> andFieldsOr(String operator, Collection<DTO> dtos) {
        return predicate("and", "or", operator, dtos);
    }

    public QueryTerm<DTO> orFieldsAnd(String operator, DTO... dtos) {
        return orFieldsAnd(operator, convert2List(dtos));
    }

    public QueryTerm<DTO> orFieldsAnd(String operator, Collection<DTO> dtos) {
        return predicate("or", "and", operator, dtos);
    }

    public QueryTerm<DTO> orFieldsOr(String operator, DTO... dtos) {
        return orFieldsOr(operator, convert2List(dtos));
    }

    public QueryTerm<DTO> orFieldsOr(String operator, Collection<DTO> dtos) {
        return predicate("or", "or", operator, dtos);
    }

    public QueryTerm<DTO> predicate(String termsComplex, String fieldsComplex, String operator, Collection<DTO> inDtos) {
        terms.add(new Term(termsComplex, fieldsComplex, operator, inDtos));
        return this;
    }

    public QueryTerm<DTO> addOrderBy(String fieldPath, boolean asc) {
        orderBy.put(fieldPath, asc);
        return this;
    }

    public QueryTerm<DTO> setPage(Integer pageNum, Integer pageSize) {
        setPage(pageNum, pageSize, false);
        return this;
    }

    public QueryTerm<DTO> setPage(Integer pageNum, Integer pageSize, Boolean memoryPaging) {
        if (pageNum != null && pageSize != null)
            this.page = new Page(pageNum, pageSize);
        this.memoryPaging = memoryPaging;
        return this;
    }

    private String doGetSubType(Class<?> aClass) {
        List<Field> typeFields = Arrays.stream(aClass.getDeclaredFields()).filter(field -> {
            QueryField queryField = field.getAnnotation(QueryField.class);
            return queryField != null && queryField.typeField();
        }).collect(toList());

        if (!typeFields.isEmpty()) {
            Field typeField = typeFields.get(0);
            if (typeField.getType().isPrimitive())
                throw new RuntimeException("域[" + typeField + "]为基础类型,无法表达null语义,建议使用对应包装类型[Integer,Boolean,...]!");
            Object fieldValue = getFieldValue(loadTemplate, typeField);
            if (fieldValue == null)
                return null;
            if (fieldValue.getClass().isEnum()) {
                return ((Enum) fieldValue).name();
            } else {
                return fieldValue.toString();
            }
        } else if (!aClass.getSuperclass().equals(Object.class)) {
            return doGetSubType(aClass.getSuperclass());
        } else
            return null;
    }

    @JsonIgnore
    public boolean isNeedPaging() {
        return page != null && page.isNotNull();
    }

    @JsonIgnore
    public boolean isMustDBPaging() {
        return isNeedPaging() && !memoryPaging;
    }

    @JsonIgnore
    public boolean isCanMemoryPaging() {
        return isNeedPaging() && memoryPaging;
    }

    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class Term<DTO> {
        private String operator;
        @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
        private Collection dtos;
        Boolean fieldBeAnd;
        Boolean termsBeAnd;

        public Term(String termsBeAnd, String fieldBeAnd, String operator, Collection<DTO> dtos) {
            switch (operator.toLowerCase()) {
                case "=":
                case "<>":
                case ">":
                case ">=":
                case "<":
                case "<=":
                case "in":
                case "between":
                case "like":
                    this.operator = operator.toLowerCase();
                    break;
                default:
                    throw new RuntimeException("无法识别的条件符号[" + operator + "]");
            }
            this.dtos = dtos;
            this.fieldBeAnd = fieldBeAnd == null ? null
                    : "and".equalsIgnoreCase(fieldBeAnd) ? true
                    : "or".equalsIgnoreCase(fieldBeAnd) ? false
                    : null;

            this.termsBeAnd = termsBeAnd == null ? null
                    : "and".equalsIgnoreCase(termsBeAnd) ? true
                    : "or".equalsIgnoreCase(termsBeAnd) ? false
                    : null;
        }

    }

    public static Object getFieldValue(Object obj, Field aField) {
        // 获取域属性名和属性值
        try {
            Object fieldValue = null;
            aField.setAccessible(true);
            fieldValue = aField.get(obj);
            return fieldValue;
        } catch (Exception e) {
            throw new RuntimeException("取得对象【" + obj + "】的域【" + aField + "】的值时发生异常：", e);
        }
    }

    @JsonIgnore
    @SneakyThrows
    public Class gatherLoadClass() {
        Class<?> templateClass = loadTemplate.getClass();
        Class<?> annoDomainClass = forName(templateClass.getAnnotation(QueryDomain.class).value());

        String subTypeIdx = doGetSubType(templateClass);
        return subTypeIdx != null
                ? subtypeCodes().getSubtype(annoDomainClass, subTypeIdx)
                : annoDomainClass;
    }

    @SneakyThrows
    @JsonIgnore
    public Class getherFromClass() {
        return forName(terms.get(0).dtos.iterator().next().getClass().getAnnotation(QueryDomain.class).value());
    }

}
