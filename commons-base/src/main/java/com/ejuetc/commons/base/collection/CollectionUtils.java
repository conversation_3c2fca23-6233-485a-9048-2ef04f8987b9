package com.ejuetc.commons.base.collection;


import com.ejuetc.commons.base.exception.CodingException;
import com.ejuetc.commons.base.utils.NumberUtils;
import com.ejuetc.commons.base.utils.StringUtils;

import java.io.IOException;
import java.io.StringReader;
import java.lang.reflect.Array;
import java.util.*;
import java.util.Map.Entry;

import static java.lang.String.valueOf;
import static java.util.Collections.emptyList;

/**
 * 数组/列表工具类;
 */
public class CollectionUtils {

    public static <E> List<E> paging(List<E> orig, Integer pageSize, Integer pageNum) {
        if (pageSize == null || pageNum == null) return orig;
        int fromIdx = pageSize * (pageNum - 1);
        if (orig.size() < fromIdx + 1) return emptyList();
        int toIndex = fromIdx + pageSize;
        if (orig.size() < toIndex) toIndex = orig.size();
        return orig.subList(fromIdx, toIndex);
    }

    public static <K, V> Map<K, V> convert2Map(List<K> keys, List<V> values) {
        Map<K, V> map = new HashMap<>();
        Iterator<K> keyIter = keys.iterator();
        Iterator<V> valueIter = values.iterator();
        while (keyIter.hasNext() && valueIter.hasNext())
            map.put(keyIter.next(), valueIter.next());
        return map;
    }


    public static <E> E getValue(Collection<E> collection, int idx) {
        int i = 0;
        for (E e : collection) {
            if (i < idx) i++;
            else return e;
        }

        throw new CodingException("[" + collection.size() + "]小于目标索引[" + idx + "]");
    }

    /**
     * 将列表转换为数组;
     *
     * @param <T>
     * @param list
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T[] listToArray(List<T> list) {
        if (list == null || list.size() == 0) {
            return null;
        }

        T[] tArr = (T[]) Array.newInstance(list.get(0).getClass(), list.size());
        return list.toArray(tArr);

    }

    public static <S, T> List<T> map(List<S> list, Maper<S, T> maper) {
        List<T> maped = new ArrayList<T>();
        for (S s : list) {
            maped.add(maper.map(s));
        }
        return maped;
    }

    public static <T> List<T> filter(List<T> list, Filter<T> filter) {
        for (Iterator<T> iterator = list.iterator(); iterator.hasNext(); ) {
            T t = (T) iterator.next();
            if (!filter.filter(t))
                iterator.remove();
        }
        return list;
    }


    /**
     * 将列表转换为数组;
     *
     * @param <T>
     * @return
     */
    public static <T> List<T> arrayToList(T[] array) {
        if (array == null) {
            return null;
        }

        return Arrays.asList(array);
    }

    /**
     * 将列表转换为数组;
     *
     * @param <T>
     * @return
     */
    public static <T> Set<T> arrayToSet(T[] array) {
        if (array == null) {
            return new HashSet<T>();
        }
        Set<T> set = new HashSet<T>();
        for (T t : array) {
            set.add(t);
        }
        return set;
    }

    public static <T> List<T> subList(List<T> list, Integer pageSize, Integer pageNO) {
        int from = (pageNO - 1) * pageSize;
        int begin = from < list.size() ? from : list.size();

        int to = from + pageSize;
        int end = to < list.size() ? to : list.size();
        return list.subList(begin, end);
    }

    public static <T> List<List<T>> splitList(List<T> srcList, int maxSize) {
        List<List<T>> splitList = new ArrayList<List<T>>();
        if (srcList == null || srcList.size() == 0) {
            return splitList;
        }

        int listSize = srcList.size();
        int splitListSize = (listSize / maxSize) + (listSize % maxSize > 0 ? 1 : 0);
        for (int i = 0; i < splitListSize; i++) {
            int beginIndex = i * maxSize;
            int endIndex = (i + 1) * maxSize;
            endIndex = endIndex > listSize ? listSize : endIndex;
            splitList.add(srcList.subList(beginIndex, endIndex));
        }
        return splitList;
    }

    public static void main(String[] args) {
        List<Integer> list = new ArrayList<Integer>();
        for (int i = 1; i <= 23; i++) {
            list.add(i);
        }

        paging(list, 10, 3).forEach(System.out::println);
    }

    /**
     * 截取直接数组
     *
     * @param srcByteArr
     * @param index
     * @param length
     * @return
     */
    @SuppressWarnings("unchecked")
    public static <T> T[] subArr(T[] srcByteArr, int index, int length) {
        // 验证参数合法性
        if (srcByteArr == null || index < 0 || length < 0 || srcByteArr.length < index + length) {
            throw new RuntimeException("参数不合法:subByteArr(T[] " + srcByteArr + ", int " + index + ", int " + length + ")");
        }

        // 构造新数据并拷贝
        T[] tArr = (T[]) Array.newInstance(srcByteArr[0].getClass(), length);
        System.arraycopy(srcByteArr, index, tArr, 0, length);
        return tArr;
    }

    /**
     * 截取直接数组
     *
     * @param srcByteArr
     * @param index
     * @param length
     * @return
     */
    public static byte[] subArr(byte[] srcByteArr, int index, int length) {
        // 验证参数合法性
        if (srcByteArr == null || index < 0 || length < 0 || srcByteArr.length < index + length) {
            throw new RuntimeException("参数不合法:subByteArr(T[] " + srcByteArr + ", int " + index + ", int " + length + ")");
        }

        // 构造新数据并拷贝
        byte[] tArr = new byte[length];
        System.arraycopy(srcByteArr, index, tArr, 0, length);
        return tArr;
    }

    /**
     * 转换数组:String[] -> Long[]
     *
     * @param strArr
     * @return
     */
    public static Long[] convertArray(String[] strArr) {
        if (strArr == null) {
            return null;
        }

        Long[] longArr = new Long[strArr.length];
        for (int i = 0; i < longArr.length; i++) {
            if (!NumberUtils.isLong(strArr[i])) {
                throw new RuntimeException("执行字符串数组到长整型数组转换过程中,发现字符串数组存在非长整型字符串[索引:" + i + " 值:" + strArr[i] + "]");
            }

            longArr[i] = Long.parseLong(strArr[i]);
        }
        return longArr;
    }

    /**
     * 将数组转换为逗号分隔的字符串
     *
     * @param objArr
     * @return
     */
    public static String arrayToString(Object[] objArr) {
        if (objArr == null || objArr.length == 0) {
            return null;
        }

        StringBuffer buf = new StringBuffer();
        for (int i = 0; i < objArr.length; i++) {
            buf.append((i > 0 ? "," : "") + objArr[i]);
        }
        return buf.toString();
    }

    /**
     * 将逗号分隔的字符串转换为数组
     *
     * @param str
     * @return
     */
    public static String[] stringToArray(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }

        String[] split = str.split(",");
        return split;
    }

    /**
     * 将一个字符串使用分隔符分割后转换为数组
     *
     * @param str
     * @param splitChar
     * @return
     */
    public static List<String> splitStringToList(String str, String splitChar) {
        if (StringUtils.isBlank(str)) {
            return null;
        }

        return arrayToList(str.split(splitChar));
    }

    /**
     * 将一个字符串使用分隔符分割后转换为数组
     *
     * @param str
     * @param splitChar
     * @return
     */
    public static List<Integer> splitStringToIntegerList(String str, String splitChar) {
        if (StringUtils.isBlank(str)) {
            return null;
        }

        String[] split = str.split(splitChar);
        List<Integer> list = new ArrayList<Integer>();
        for (String string : split) {
            list.add(Integer.valueOf(string));
        }
        return list;
    }

    /**
     * 判断一个列表是否为空
     *
     * @param list
     * @return
     */
    public static boolean isEmpty(List<?> list) {
        return list == null || list.size() == 0;
    }

    /**
     * 判断一个列表是否为空
     *
     * @param list
     * @return
     */
    public static boolean isEmpty(String[] list) {
        return list == null || list.length == 0;
    }

    /**
     * 判断一个对象列表是否为空
     *
     * @param objects
     * @return
     */
    public static boolean isEmpty(Object[] objects) {
        return objects == null || objects.length == 0;
    }


    /**
     * 将Map<String,String[]>转换为Map<String,String>
     *
     * @param srcMap
     * @return
     */
    public static Map<String, String> convertMap(Map<String, String[]> srcMap) {
        Map<String, String> aimMap = new HashMap<String, String>();
        for (Entry<String, String[]> entry : srcMap.entrySet()) {
            String[] srcValue = entry.getValue();
            String aimValue = srcValue != null && srcValue.length > 0 ? srcValue[0] : null;
            aimMap.put(entry.getKey(), aimValue);
        }
        return aimMap;
    }

    /**
     * 转换properties为map
     *
     * @param properties
     * @return
     */
    public static Map<String, String> convertProperties2Map(Properties properties) {
        if (properties == null) {
            return null;
        }

        Map<String, String> map = new LinkedHashMap<String, String>();
        Set<Entry<Object, Object>> entrySet = properties.entrySet();
        for (Entry<Object, Object> entry : entrySet) {
            map.put(valueOf(entry.getKey()), valueOf(entry.getValue()));
        }

        return map;
    }

    /**
     * 判断两个集合是否包含重复项
     *
     * @param list1
     * @param list2
     * @return
     */
    public static boolean isOverlap(Collection<?> list1, Collection<?> list2) {
        if (list1 == list2) {
            return true;
        }

        if (list1 == null || list2 == null) {
            return false;
        }

        for (Object element : list1) {
            if (list2.contains(element)) {
                return true;
            }
        }

        return false;
    }

    public static String map2String(Map<?, ?> map) {
        return map2String(map, ",");
    }

    public static String map2String(Map<?, ?> map, String splitChar) {
        if (map == null) return null;
        StringBuffer buf = new StringBuffer();
        for (Entry<?, ?> entry : map.entrySet()) {
            if (buf.length() > 0) buf.append(splitChar);
            buf.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return buf.toString();
    }

    /**
     * 将字符串列表内容都改为小写
     *
     * @param srcList
     * @return
     */
    public static List<String> toLowerCase(List<String> srcList) {
        if (srcList == null) {
            return null;
        }

        List<String> aimList = new ArrayList<String>();
        for (String string : srcList) {
            aimList.add(StringUtils.toLowerCase(string));
        }
        return aimList;
    }

    /**
     * 将字符串列表内容都改为小写
     *
     * @param srcList
     * @return
     */
    public static Set<String> toLowerCase(Set<String> srcList) {
        if (srcList == null) {
            return null;
        }

        Set<String> aimList = new HashSet<String>();
        for (String string : srcList) {
            aimList.add(StringUtils.toLowerCase(string));
        }
        return aimList;
    }

    public static <T> Map<String, List<T>> convert2Map(List<T> valueList, GetKey<T> getKey) {
        Map<String, List<T>> map = new HashMap<String, List<T>>();
        for (T value : valueList) {
            String key = getKey.getKey(value);
            List<T> mapedValueList = map.get(key);
            if (mapedValueList == null) {
                mapedValueList = new ArrayList<T>();
                map.put(key, mapedValueList);
            }
            mapedValueList.add(value);
        }
        return map;
    }

    public static Map makeMap(Object... v) {
        if (v.length % 2 != 0) throw new RuntimeException("值长度[" + v.length + "]不是2的倍数,不能映射成Map!");

        Map map = new LinkedHashMap<Object, Object>();
        for (int i = 0; i + 1 < v.length; i += 2) {
            map.put(v[i], v[i + 1]);
        }
        return map;
    }

    public static Map<String, String> makeStringMap(Object... v) {
        if (v.length % 2 != 0) throw new RuntimeException("值长度[" + v.length + "]不是2的倍数,不能映射成Map!");

        Map<String, String> map = new LinkedHashMap<String, String>();
        for (int i = 0; i + 1 < v.length; i += 2) {
            map.put(valueOf(v[i]), valueOf(v[i + 1]));
        }
        return map;
    }

    public static <K, V> Map<K, V> filterMap(Map<K, V> params) {
        if (params == null || params.isEmpty()) return params;
        Map<K, V> result = new HashMap();
        for (K key : params.keySet()) {
            if (key == null || key.equals("")) continue;
            V value = params.get(key);
            if (value == null || value.equals("")) continue;
            result.put(key, value);
        }
        return result;
    }

    public static Map<String, String> convertProperties2Map(String values) throws IOException {
        Map<String, String> fieldValuesMap = new HashMap<>();
        Properties props = new Properties();
        props.load(new StringReader(values));
        Enumeration<?> names = props.propertyNames();
        while (names.hasMoreElements()) {
            String fieldName = (String) names.nextElement();
            String fieldStrValue = (String) props.get(fieldName);
            fieldValuesMap.put(fieldName, fieldStrValue);
        }
        return fieldValuesMap;
    }

    public static interface GetKey<T> {
        String getKey(T obj);
    }

    public static interface Filter<T> {
        boolean filter(T t);
    }

    public static interface Maper<S, T> {
        T map(S s);
    }

    public static <K, V> Map<K, V> map(K k1, V v1) {
        Map<K, V> map = new HashMap<>();
        map.put(k1, v1);
        return map;
    }

    public static <K, V> Map<K, V> map(K k1, V v1, K k2, V v2) {
        Map<K, V> map = new HashMap<>();
        map.put(k1, v1);
        map.put(k2, v2);
        return map;
    }

    public static <K, V> Map<K, V> map(K k1, V v1, K k2, V v2, K k3, V v3) {
        Map<K, V> map = new HashMap<>();
        map.put(k1, v1);
        map.put(k2, v2);
        map.put(k3, v3);
        return map;
    }

    public static <K, V> Map<K, V> map(K k1, V v1, K k2, V v2, K k3, V v3, K k4, V v4) {
        Map<K, V> map = new HashMap<>();
        map.put(k1, v1);
        map.put(k2, v2);
        map.put(k3, v3);
        map.put(k4, v4);
        return map;
    }

}
