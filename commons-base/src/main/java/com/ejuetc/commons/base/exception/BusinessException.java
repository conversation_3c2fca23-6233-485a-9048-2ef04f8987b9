package com.ejuetc.commons.base.exception;

import com.ejuetc.commons.base.clazz.ClasspathFileLoad;
import lombok.Getter;

import java.text.MessageFormat;
import java.util.*;

@Getter
public class BusinessException extends RuntimeException {
    @Getter(lazy = true)
    private final static Map<String, String> codeMap = makeCodeMap();
    private Error error;

    public BusinessException(String code, Object... args) {
        super(getBusinessInfo(code, args));
        this.error =new Error(code,args,getMessage());
    }

    public static String getBusinessInfo(String code, Object... args) {
        String infoTemp = getCodeMap().get(code);
        if (infoTemp == null)
            throw new RuntimeException("未找到业务码[" + code + "]!");
        return MessageFormat.format(infoTemp, args);
    }

    private static Map<String, String> makeCodeMap() {
        Map<String, String> code2InfoMap = new HashMap<>();
        Map<String, String> code2PathMap = new HashMap<>();
        Set<String> bcFilePaths = new ClasspathFileLoad().findFilePaths("", "bc_", ".properties");
        bcFilePaths.forEach(fullFilePath -> {
            ResourceBundle resourceBundle = getResourceBundle(fullFilePath);
            resourceBundle.keySet().forEach(code -> {
                String existPath = code2PathMap.get(code);
                if (existPath != null) {
                    throw new RuntimeException("[" + fullFilePath + "]和[" + existPath + "]存在相同code[" + code + "]!");
                }

                code2InfoMap.put(code, resourceBundle.getString(code));
                code2PathMap.put(code, fullFilePath);
            });
        });
        return code2InfoMap;
    }

    private static ResourceBundle getResourceBundle(String fullFilePath) {
        int startIndex = fullFilePath.startsWith("/") ? 1 : 0;
        int endIndex = fullFilePath.lastIndexOf(".");
        String subPath = fullFilePath.substring(startIndex, endIndex);
        ResourceBundle resourceBundle = ResourceBundle.getBundle(subPath);
        return resourceBundle;
    }

    public static void main(String[] args) {
        System.out.println(new BusinessException("bc.commons.base.001", 1, "a", true));
    }

}
