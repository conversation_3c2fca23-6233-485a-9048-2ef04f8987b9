package com.ejuetc.commons.base.spring;

import com.ejuetc.commons.base.exception.BusinessException;
import com.ejuetc.commons.base.exception.ParamException;
import com.ejuetc.commons.base.response.ApiResponse;
import com.ejuetc.commons.base.response.ResponseStatus;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;

import java.util.Map;
import java.util.Set;

import static com.ejuetc.commons.base.response.ApiResponse.apiResponse;
import static com.ejuetc.commons.base.utils.IOUtils.readHttpBody;
import static com.ejuetc.commons.base.utils.ServletUtils.getHeaders;
import static java.util.stream.Collectors.toMap;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @Autowired
    private HttpServletRequest request;

    // 定义需要排除的路径
    private static final Set<String> EXCLUDED_PATHS = Set.of("/api/healthCheck");

    private boolean isExcludedPath() {
        String requestURI = request.getRequestURI();
        return EXCLUDED_PATHS.contains(requestURI);
    }

    //处理 form data方式调用接口校验失败抛出的异常
    @ExceptionHandler({BindException.class})
    public ApiResponse bindExceptionHandler(BindException e) {
        log.error("", e);
        Map<String, String> fieldName2ErrorMap = e.getBindingResult().getFieldErrors().stream().collect(toMap(FieldError::getField, FieldError::getDefaultMessage));
        return apiResponse(ResponseStatus.FAIL_PRM).setError(fieldName2ErrorMap);
    }

    //  处理 json 请求体调用接口校验失败抛出的异常
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        log.error("", e);
        Map<String, String> fieldName2ErrorMap = e.getBindingResult().getFieldErrors().stream().collect(toMap(FieldError::getField, FieldError::getDefaultMessage));
        return apiResponse(ResponseStatus.FAIL_PRM).setError(fieldName2ErrorMap);
    }

    //  处理单个参数校验失败抛出的异常
    @ExceptionHandler(ConstraintViolationException.class)
    public ApiResponse constraintViolationExceptionHandler(ConstraintViolationException e) {
        log.error("", e);
        Set<ConstraintViolation<?>> constraintViolations = e.getConstraintViolations();
        Map<String, String> fieldName2ErrorMap = constraintViolations.stream().collect(toMap(c -> {
            String propertyPath = c.getPropertyPath().toString();
            int dotIdx = propertyPath.lastIndexOf(".");
            return dotIdx < 0 ? propertyPath : propertyPath.substring(dotIdx + 1);
        }, ConstraintViolation::getMessage));
        return apiResponse(ResponseStatus.FAIL_PRM).setError(fieldName2ErrorMap);
    }

    @ExceptionHandler(ParamException.class)
    public ApiResponse handlerParamException(ParamException e) {
        log.error("", e);
        return apiResponse(ResponseStatus.FAIL_PRM, e.getMessage(), null).setError(Map.of(e.getParamName(), e.getParamFormat()));
    }

    @ExceptionHandler(BusinessException.class)
    public ApiResponse handlerBusinessException(BusinessException e) {
        log.error("", e);
        return apiResponse(e);
    }

    @ExceptionHandler(Exception.class)
    public ApiResponse handlerException(Exception e) throws Exception {
        if (isExcludedPath()) {
            // 如果是排除的路径，直接返回 null 或其他处理逻辑
            log.warn("Exception occurred on excluded path: {}", request.getRequestURI());
            throw e;
        }
        log.error("", e);
        return apiResponse(e);
    }

    @ExceptionHandler(ServletRequestBindingException.class)
    @org.springframework.web.bind.annotation.ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public String handleMissingRequestAttributeException(ServletRequestBindingException ex) {
        // 返回错误信息或自定义响应格式
        return "Missing required request attribute: " + ex.getMessage();
    }
}