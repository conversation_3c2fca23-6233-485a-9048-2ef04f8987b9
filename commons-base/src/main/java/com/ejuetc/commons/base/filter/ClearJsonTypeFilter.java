package com.ejuetc.commons.base.filter;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Collection;
import java.util.regex.Pattern;

import static com.ejuetc.commons.base.utils.NumberUtils.isNumber;
import static java.lang.Class.forName;
import static java.lang.String.valueOf;
import static java.util.regex.Pattern.compile;

/**
 * 过滤应答JSON中的属性类型信息(仅用于供前端调用模块) 林冬成20230625
 */
@Slf4j
public class ClearJsonTypeFilter extends BaseJsonFilter {
    public static final Pattern compile = Pattern.compile("(\"java(\\.[\\w\\$]+)+\")|\"@class\"");

    @Override
    protected void filter(HttpServletRequest httpRequest, HttpServletResponse httpResponse, FilterChain filterChain) throws ServletException, IOException {
        if (httpRequest.getRequestURI().contains("/api/")
        		|| httpRequest.getRequestURI().contains("/web/search/doSearch")
        		|| httpRequest.getRequestURI().contains("/web/search/doSearch1")) {
            filterChain.doFilter(httpRequest, httpResponse);
        } else {
            filterChain.doFilter(httpRequest, new JSONTypeCleanerResponse(httpResponse));
        }
    }

    class JSONTypeCleanerResponse extends BufferResponse {

        public JSONTypeCleanerResponse(HttpServletResponse response) throws IOException {
            super(response);
        }

        protected String convertContent(String content) {
            return new JSONTypeCleaner(content).clear();
        }

    }

    class JSONTypeCleaner {
        public String origContent;

        public JSONTypeCleaner(String origContent) {
            this.origContent = origContent;
        }

        public String clear() {
            if (!isHasTypeInfo()) return origContent;

            Object from = JSON.parse(origContent);
            Object to = deepCopy(from);
            return JSON.toJSONString(to, true);
        }

        private boolean isHasTypeInfo() {
            return compile.matcher(origContent).find();
        }

        @SneakyThrows
        private Object deepCopy(Object value) {
            if (value instanceof JSONObject) {
                JSONObject toJsonObj = new JSONObject();
                ((JSONObject) value).forEach((k, v) -> {
                    if (!k.equals("@class")) {
                        toJsonObj.put(k, deepCopy(v));
                    }
                });
                return toJsonObj;
            } else if (value instanceof JSONArray) {
                JSONArray fromArr = (JSONArray) value;
                if (fromArr.size() == 2) {
                    Object value0 = fromArr.get(0);
                    Object value1 = fromArr.get(1);
                    if (value0 instanceof String
                        && ((String) value0).matches("^([a-z]+\\.)+[\\w\\$]+")
                        && Collection.class.isAssignableFrom(forName(((String) value0)))
                        && value1 instanceof JSONArray) {
                        fromArr = (JSONArray) value1;
                    }
                    if (value0 instanceof String
                        && (value0.equals("java.math.BigDecimal") || value0.equals("java.lang.Long"))
                        && isNumber(valueOf(value1))) {
                        return value1;
                    }
                }

                JSONArray toArr = new JSONArray();
                fromArr.forEach(fromArrValue -> {
                    toArr.add(deepCopy(fromArrValue));
                });
                return toArr;
            } else {
                return value;
            }
        }
    }

    @SneakyThrows
    public static void main(String[] args) {
        boolean assignableFrom = Collection.class.isAssignableFrom(forName("com.alibaba.fastjson.JSONArray"));
        System.out.println(assignableFrom);
    }

}



