package com.ejuetc.commons.base.response;

import lombok.Getter;

/**
 * 应答状态
 */
@Getter
public enum ResponseStatus {
    WAIT("待发起", null),

    SUCC_DONE("成功", true),
    SUCC_PART("部分成功", true),
    SUCC_ING("已接收处理中", true),
    SUCC_CONT("继续下一步", true),

    FAIL_PRM("参数错误", false),
    FAIL_BIZ("业务错误", false),
    FAIL_SYS("系统错误", false),//当异常可回滚时返回

    UNKNOW("处理结果未知", null),//当有外部调用未知时(不可回滚)
    ;

    private final String message;   //对应信息
    private final Boolean group;    //分组(true:成功,false:失败,null:未知)

    ResponseStatus(String message, Boolean group) {
        this.message = message;
        this.group = group;
    }

    public boolean isSucc() {
        return group != null && group;
    }

    public boolean isFail() {
        return group != null && !group;
    }

    public boolean isUnknow() {
        return group == null;
    }

    @Override
    public String toString() {
        return name() + "-" + message;
    }
}
