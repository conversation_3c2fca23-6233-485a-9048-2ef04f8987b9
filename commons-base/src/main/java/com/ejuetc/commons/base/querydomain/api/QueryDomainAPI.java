package com.ejuetc.commons.base.querydomain.api;


import com.ejuetc.commons.base.response.ResponseException;
import com.ejuetc.commons.base.querydomain.api.QueryResponse;
import com.ejuetc.commons.base.querydomain.api.QueryTerm;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.core.codec.CodecException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Tag(name = "API_查询服务")
public interface QueryDomainAPI {

    @Operation(summary = "简单查询单个DTO")
    @PostMapping("/api/queryDomain/queryDTO")
    default <DTO> DTO queryDTO(@RequestBody DTO dto) {
        List<DTO> dtos = query(dto);
        if (dtos == null || dtos.isEmpty())
            return null;
        if (dtos.size() > 1)
            throw new CodecException("查询结果非单行!");
        return dtos.get(0);
    }

    @Operation(summary = "简单查询DTO列表")
    @PostMapping("/api/queryDomain/queryList")
    default <DTO> List<DTO> query(@RequestBody DTO dto) {
        QueryResponse<DTO> queryRps = query(new QueryTerm<>(dto).beginFieldsAnd("=", dto));
        if (!queryRps.isSucc()) throw new ResponseException(queryRps);
        return queryRps.getData();
    }

    @Operation(summary = "复杂查询")
    @PostMapping("/api/queryDomain/queryByTerm")
    <DTO> QueryResponse<DTO> query(@RequestBody QueryTerm<DTO> queryTerm);
}
