package com.ejuetc.commons.base.querydomain.api;

import java.util.Collection;

public enum LoadPolicy {
    JUDGE_LOAD      //判断加载:无"@QueryDomain"标注类型默认加载,有标注类型指定加载
    , ALWAYS_LOAD   //总是加载:无论是否基础类型均默认加载
    , SPECIFY_LOAD  //指定加载:仅指定时加载
    ;

    public boolean isLoad(boolean entityType, Object templateFieldValue) {
        boolean templateFieldHasValue = !(templateFieldValue == null || templateFieldValue instanceof Collection<?>  && ((Collection)templateFieldValue).isEmpty());
        return switch (this) {
            case JUDGE_LOAD -> !entityType || templateFieldHasValue;
            case ALWAYS_LOAD -> true;
            case SPECIFY_LOAD -> templateFieldHasValue;
        };
    }
}
