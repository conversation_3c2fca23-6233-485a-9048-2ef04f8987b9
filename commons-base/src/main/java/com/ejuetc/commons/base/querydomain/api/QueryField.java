package com.ejuetc.commons.base.querydomain.api;

import jakarta.persistence.criteria.JoinType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface QueryField {
    String value() default "";

    boolean typeField() default false; //用于确定子类型

    LoadPolicy loadPolicy() default LoadPolicy.JUDGE_LOAD;

    boolean loadOnly() default false;//标注属性只用于加载,不参与查询条件

    JoinType joinType() default JoinType.INNER;
}
