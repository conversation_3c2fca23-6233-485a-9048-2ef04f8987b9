package com.ejuetc.commons.base.utils;

import com.alibaba.fastjson.JSON;
import com.ejuetc.commons.base.exception.CodingException;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static java.nio.charset.StandardCharsets.UTF_8;
import static java.time.LocalDateTime.now;
import static java.time.format.DateTimeFormatter.ofPattern;
import static java.util.Collections.emptyMap;
import static java.util.Objects.requireNonNull;

@Slf4j
public class IOUtils {
    public static final String FILE_PROXY = "file://";
    public static final String CLASSPATH_PROXY = "classpath://";
    private static final RestTemplate restTemplate = new RestTemplate(List.of(new StringHttpMessageConverter(StandardCharsets.UTF_8)));

    @SneakyThrows
    public static String httpGet(String url, Map<String, Object[]> params) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        params.forEach(builder::queryParam);
        String uriString = builder.toUriString();
        return new String(readUrlToByteArray(uriString), UTF_8);
    }

    public static ResponseEntity<String> httpPostJson(String url, Map<String, String> headerMap, String jsonBody) {
        return httpPostJson(url, headerMap, emptyMap(), jsonBody);
    }

    public static ResponseEntity<String> httpPostJson(String url, Map<String, String> headerMap, Map<String, Object[]> params, String jsonBody) {
        HttpHeaders headers = new HttpHeaders();
        if (headerMap != null && !headerMap.isEmpty()) headerMap.forEach(headers::add);
        if (params != null && !params.isEmpty()) {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
            params.forEach(builder::queryParam);
            url = builder.build().toUriString();
        }
        headers.add("Content-type", "application/json");
        log.info("httpPost requestBody={}, headers={}", jsonBody, headers);
        HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);
        ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        log.info("httpPost responseEntityBody={}", responseEntity.getBody());
        return responseEntity;
    }


    public static byte[] readUrlToByteArray(String urlString) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");

        try (InputStream inputStream = connection.getInputStream();
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }

            return byteArrayOutputStream.toByteArray();
        } finally {
            connection.disconnect();
        }
    }

    public static String readHttpBody(HttpServletRequest request) throws IOException {
//        StringBuilder requestBodyBuilder = new StringBuilder();
//        try (ServletInputStream inputStream = request.getInputStream()) {
//            byte[] buffer = new byte[1024];
//            int bytesRead;
//            while ((bytesRead = inputStream.read(buffer)) != -1) {
//                requestBodyBuilder.append(new String(buffer, 0, bytesRead, UTF_8));
//            }
//        }
//        return requestBodyBuilder.toString();


        //林冬成-250226: 解决读取请求出现乱码的问题
//        StringBuilder stringBuilder = new StringBuilder();
//        try (BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream(), StandardCharsets.UTF_8))) {
//            String line;
//            while ((line = reader.readLine()) != null) {
//                stringBuilder.append(line);
//            }
//        }
//        return stringBuilder.toString();

        //林冬成-250422,解决读取内容和返回内容不一致问题(造成网关验签失败)
        try (ServletInputStream inputStream = request.getInputStream();
             ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, bytesRead);
            }
            return byteArrayOutputStream.toString(UTF_8);
        }

    }

    public static String read(InputStream in) {
        byte[] buf = new byte[1024];
        int length = -1;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            while ((length = in.read(buf)) != -1) {
                out.write(buf, 0, length);
            }
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage(), e);
        }

        return out.toString();
    }

    @SneakyThrows
    public static String read(BufferedReader reader) {
        StringBuilder buf = new StringBuilder();
        String line = null;
        while ((line = reader.readLine()) != null && !line.isEmpty()) {
            buf.append(line);
        }
        return buf.toString();
    }

    public static boolean isPath(String str) {
        return str != null && str.matches("^(\\w+:/)?/?(/[\\w-]+)+(\\.\\w+)?$|^\\.(/[\\w-]+)+(\\.\\w+)?$");
    }

    @SneakyThrows
    public static String read(String path) {
        if (!isPath(path))
            throw new CodingException("[" + path + "]非文件路径,无法读取!");
        return read(path.startsWith(CLASSPATH_PROXY)
                ? requireNonNull(Thread.currentThread().getContextClassLoader().getResourceAsStream(path.substring(CLASSPATH_PROXY.length())))
                : path.startsWith(FILE_PROXY)
                ? new FileInputStream(path.substring(FILE_PROXY.length()))
                : new FileInputStream(path));
    }

    public static void main2(String[] args) {
        String[] content = new String[]{
                "/Users/<USER>/IdeaProjects/ejuetc/commons3/commons-base/src/main/resources/template/enterFunction.ftl",
                "file:///Users/<USER>/IdeaProjects/ejuetc/commons3/commons-base/src/main/resources/template/enterFunction.ftl",
                "classpath://template/enterFunction.ftl", "./aaa.html", "123", "{}"
        };
        for (String path : content) {
            System.out.println(isPath(path));
        }
    }

    public static void main(String[] args) {
        String path = "https://test-tmhouse-oss.ebaas.com/0I6FOPQMZHIODC0B/69/IMAGE/1701843736043/009ea64751d942b49d1e71d8d6f78242";
        System.out.println(getUrlContentLength(path));
    }

    @SneakyThrows
    public static long getUrlContentLength(String url) {
        HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
        try {
            connection.setRequestMethod("GET");
            int responseCode = connection.getResponseCode();
            if (responseCode != 200)
                throw new RuntimeException("请求url应答异常[" + responseCode + "]:" + url);
            return Long.parseLong(connection.getHeaderField("Content-Length"));
        } finally {
            connection.disconnect();
        }
    }


    @SneakyThrows
    public static void execCommand(String command) {
        log.info("exec command : \n{}", command);
        ProcessBuilder processBuilder = new ProcessBuilder("/bin/sh", "-c", command);
        Process process = processBuilder.start();

        Thread outputThread = new Thread(() -> {
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    log.info(line);
                }
            } catch (IOException e) {
                log.error("Error reading command output", e);
            }
        });

        Thread errorThread = new Thread(() -> {
            try (BufferedReader errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    log.info(line);
                }
            } catch (IOException e) {
                log.error("Error reading command error output", e);
            }
        });

        outputThread.start();
        errorThread.start();

        int exitCode = process.waitFor();
        outputThread.join();
        errorThread.join();

        if (exitCode != 0) {
            throw new RuntimeException("Command execution failed with exit code [" + exitCode + "]:\n" + command);
        }
    }

    public static String tempFile(String postfix) {
        String absolutePath = System.getProperty("user.home") + File.separator + "ejuetc_temp" + File.separator + now().format(ofPattern("yyyyMMddHHmmss")) + "_" + UUID.randomUUID().toString().replaceAll("-", "") + postfix;
        File parentDir = new File(absolutePath).getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            if (!parentDir.mkdirs()) {
                throw new RuntimeException("Failed to create directory: " + parentDir);
            }
        }
        return absolutePath;
    }

}
