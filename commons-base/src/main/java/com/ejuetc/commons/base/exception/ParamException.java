package com.ejuetc.commons.base.exception;

import lombok.Getter;

/**
 * 编码异常:出现代码逻辑问题
 */
@Getter
public class ParamException extends RuntimeException {

    private final String paramName;

    private final Object paramValue;

    private final String paramFormat;

    public ParamException(String paramName, Object paramValue, String paramFormat) {
        super("参数[%s]当前值[%s]不合法,正确格式为[%s]".formatted(paramName, paramValue, paramFormat));
        this.paramName = paramName;
        this.paramValue = paramValue;
        this.paramFormat = paramFormat;
    }

    public ParamException(String paramName, String paramFormat) {
        this(paramName,null, paramFormat);
    }
}
