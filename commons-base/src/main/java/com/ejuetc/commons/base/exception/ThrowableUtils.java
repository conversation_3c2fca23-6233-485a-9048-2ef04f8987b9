package com.ejuetc.commons.base.exception;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * token工具类
 *
 * <AUTHOR>
 * @description
 */
public class ThrowableUtils {

    /**
     * 获取Throwable信息
     *
     * @param t
     * @return
     */
    public static String throwStackTrace(Throwable t) {
        if (t == null) return null;

        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);
        t.printStackTrace(printWriter);

        return stringWriter.toString();
    }

    /**
     * 取得异常的根信息
     *
     * @param t
     * @return
     */
    public static Throwable getRootCause(Throwable t) {
        Throwable cause = t.getCause();
        return cause != null ? getRootCause(cause) : t;
    }


    public static String getRootMessage(Throwable t) {
        return getRootCause(t).getMessage();
    }
}
