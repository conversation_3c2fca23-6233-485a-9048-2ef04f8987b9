apiVersion: v1
kind: ConfigMap
metadata:
  namespace: dcai-prod
  name: nginx-cfg
data:
  # [AI销冠]微信小程序验证文件
  QZkIVgVJh8.txt: 29ce63f55ebef5eefa5e86c5fad6df7f
  # nginx配置文件
  default.conf: |
    server {
        listen 80;
        server_name dcai.ebaas.com;

        # 代理重定向配置 - 处理 /consumer/ 路径
        location /consumer/ {
            proxy_pass https://cons.ebaas.com;
            proxy_set_header Host cons.ebaas.com;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 静态文件服务配置 - 处理其他路径（包括微信验证文件）
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ =404;
    
            # 为微信验证文件设置正确的Content-Type
            location ~* \.txt$ {
                add_header Content-Type text/plain;
            }
        }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: dcai-prod
  name: nginx-dpt
  labels:
    app: nginx-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx-app
  template:
    metadata:
      labels:
        app: nginx-app
    spec:
      volumes:
        # nginx配置文件卷
        - name: nginx-vlm-conf
          configMap:
            name: nginx-cfg
            items:
              - key: default.conf
                path: default.conf
        # 静态文件卷
        - name: nginx-vlm-static
          configMap:
            name: nginx-cfg
            items:
              - key: QZkIVgVJh8.txt
                path: QZkIVgVJh8.txt
      containers:
        - name: nginx-ctn
          image: nginx:latest
          ports:
            - containerPort: 80
          volumeMounts:
            # 挂载nginx配置文件
            - name: nginx-vlm-conf
              mountPath: /etc/nginx/conf.d
              readOnly: true
            # 挂载静态文件（包括微信验证文件）
            - name: nginx-vlm-static
              mountPath: /usr/share/nginx/html
              readOnly: true
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-svc
  namespace: dcai-prod
spec:
  selector:
    app: nginx-app
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-igr
  namespace: dcai-prod
  annotations:
    mse.aliyun.com/service-type: "MSE"  # 声明使用MSE网关
spec:
  ingressClassName: mse
  tls:
    - hosts:
        - dcai.ebaas.com
      secretName: dcai.ebaas.com-tls-secret
  rules:
    - host: dcai.ebaas.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: nginx-svc
                port:
                  number: 80
---
# HPA自动伸缩配置
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: nginx-hpa
  namespace: dcai-prod
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: nginx-dpt
  minReplicas: 2
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 75