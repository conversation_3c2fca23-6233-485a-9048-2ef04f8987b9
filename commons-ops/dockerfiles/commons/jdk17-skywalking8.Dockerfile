# 基于 alinux3 的基础镜像
FROM alibaba-cloud-linux-3-registry.cn-hangzhou.cr.aliyuncs.com/alinux3/alinux3:latest

# 维护者信息
MAINTAINER <EMAIL>

# 设置时区
ENV TimeZone=Asia/Shanghai

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TimeZone /etc/localtime && echo $TimeZone > /etc/timezone

# 安装必要工具和 OpenJDK 17
RUN yum update -y && \
    yum install -y java-17-openjdk wget tar telnet iputils && \
    yum clean all

# 下载并安装 SkyWalking Agent
ENV SKYWALKING_VERSION=8.15.0
RUN wget https://archive.apache.org/dist/skywalking/java-agent/${SKYWALKING_VERSION}/apache-skywalking-java-agent-${SKYWALKING_VERSION}.tgz && \
    tar -xzf apache-skywalking-java-agent-${SKYWALKING_VERSION}.tgz -C /. && \
    rm -f apache-skywalking-java-agent-${SKYWALKING_VERSION}.tgz

# 验证JAVA安装
CMD ["java", "-version"]
