## 公共部分
```shell
docker login --username=不动产交易协作 image-store-registry.cn-shanghai.cr.aliyuncs.com
```

## 构建 [alinux3-openjdk17-skywalking8.Dockerfile](jdk17-skywalking8.Dockerfile)
```shell
docker build -t jdk17-skywalking8:v250603-1 -f jdk17-skywalking8.Dockerfile . 
docker tag sha256:a13d2e5a33eb323ed814d882932c4547f18e784d6b2bc015579977ec3ddd5b26 image-store-registry.cn-shanghai.cr.aliyuncs.com/common/jdk17-skywalking8:v250603-1
docker push image-store-registry.cn-shanghai.cr.aliyuncs.com/common/jdk17-skywalking8:v250603-1
```

## 构建 [alinux3-node-npm.Dockerfile](alinux3-node-npm.Dockerfile)
```shell
docker build --no-cache --progress=plain -t alinux3-node-npm:v250516-1 -f alinux3-node-npm.Dockerfile . 
docker tag d19c4c41e80e image-store-registry.cn-shanghai.cr.aliyuncs.com/eju-cpm/alinux3-node22.15.1-npm10.9.2:v250516-1
docker push image-store-registry.cn-shanghai.cr.aliyuncs.com/eju-cpm/alinux3-node22.15.1-npm10.9.2:v250516-1
```

docker tag sha256:3bf7cda95541c7c761cae4bbf808f9494681beb4294f59fd18a28196371bcb16 image-store-registry.cn-shanghai.cr.aliyuncs.com/common/node:18.20.3-alpine
docker push image-store-registry.cn-shanghai.cr.aliyuncs.com/common/node:18.20.3-alpine