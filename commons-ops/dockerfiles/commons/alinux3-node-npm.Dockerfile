# 基于 alinux3 的基础镜像
FROM alibaba-cloud-linux-3-registry.cn-hangzhou.cr.aliyuncs.com/alinux3/alinux3:latest

# 维护者信息
MAINTAINER <EMAIL>

# 设置时区
ENV TimeZone=Asia/Shanghai

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TimeZone /etc/localtime && echo $TimeZone > /etc/timezone

# 安装必要工具和 Node.js
RUN yum update -y && \
    yum install -y wget tar && \
    curl -fsSL https://rpm.nodesource.com/setup_18.x | bash - && \
    yum install -y nodejs && \
    yum clean all

# 验证 Node.js 和 npm 安装
RUN node -v && npm -v
