package com.ejuetc.commons.integration.user;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "merchant", contextId = "MerchantApi")
public interface MerchantApi {

    /**
     * 查询业绩信息
     * 
     * @param inviteCode 推荐码
     * @param userId userId
     * @param cityId saas的城市id
     * @return 业绩信息
     */
    @GetMapping("/v2/repo/mc/employees/performance-attribution")
    PerformanceAttributionRO getPerformanceAttribution(
            @RequestParam("inviteCode") String inviteCode,
            @RequestParam("userId") Long userId,
            @RequestParam("cityId") Integer cityId);


    /**
     * 根据employeeId查询EmployeeRO
     */
    @GetMapping("/v2/repo/mc/employees/detail-with-deleted/{id}")
    EmployeeRO getEmployee(@PathVariable("id") Long id);

    /**
     * 根据userId查询公司和员工信息
     */
    @GetMapping("/v2/repo/mc/employees/employee-company-info")
    EmployeeCompanyRO getEmployeeCompany(@RequestParam("userId") Long userId);

    /**
     * 用户认证结果通知
     *
     * @param userId
     */
    @PostMapping("/v2/repo/mc/employees/user-auth-callback")
    void userAuthCallback(@RequestParam("userId") Long userId);
    
    /**
     * 通过公司userId查询employee
     *
     * @param userId
     * @return
     */
    @GetMapping("/v2/repo/mc/employees/get-company-employee")
    EmployeeRO getCompanyEmployee(@RequestParam("userId") Long userId);

}