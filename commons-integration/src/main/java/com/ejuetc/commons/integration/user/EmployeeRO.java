package com.ejuetc.commons.integration.user;

import lombok.Data;

@Data
public class EmployeeRO {
    private Long id;
    private Long merchantId;
    private Long departmentId;
    private Long roleId;
    private Long userId;
    private DepartmentRO department;
    private MerchantRO merchant;

    private String name;
    private String phone;
    private String workPhone;
    /**
     * 个人版saas的真实手机号
     */
    private String personalPhone;
    /**
     * 状态：17=在职，18=离职，19=禁用
     */
    private Integer status;
    private Integer deleted;
}
