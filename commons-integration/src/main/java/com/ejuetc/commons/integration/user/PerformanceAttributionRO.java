package com.ejuetc.commons.integration.user;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 业绩归属信息
 */
@NoArgsConstructor
@Data
@Accessors(chain = true)
public class PerformanceAttributionRO {
    /**
     * 邀请码对应的业绩归属人CRM工号
     */
    private String performanceUserNo;
    /**
     * 邀请码对应的业绩归属人CRM姓名
     */
    private String performanceUserName;

    /**
     * 邀请码对应的TP公司编号
     */
    private String tpCompanyNo;
    /**
     * 邀请码对应的TP公司名称
     */
    private String tpCompanyName;

    /**
     * 城发拓展人工号(TP才有，和performanceUserNo一致)
     */
    private String cfPerformanceUserNo;
    /**
     * 城发拓展员姓名(TP才有，和performanceUserName一致)
     */
    private String cfPerformanceUserName;


    /**
     * 用户当前在职的公司名称
     */
    private String companyName;
    /**
     * 用户当前在职的公司编号(个人版可能没有)
     */
    private String companyNo = "-";


    /**
     * 业绩归属城市
     */
    private String performanceCityCode;
}
