<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.6</version>
        <relativePath /> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.ejuetc.commons</groupId>
    <artifactId>commons</artifactId>
    <version>0.7.2</version>
    <packaging>pom</packaging>

    <modules>
        <module>commons-base</module>
        <module>commons-eureka</module>
        <module>commons-querydomain</module>
        <module>commons-integration</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <spring-cloud.version>2023.0.2</spring-cloud.version>
        <fastjson.version>1.2.83</fastjson.version>
        <jetbrains.version>RELEASE</jetbrains.version>
        <guava.version>30.0-jre</guava.version>
        <apm.version>8.15.0</apm.version>
        <jwt.version>3.4.0</jwt.version>
        <tidb-mysql.version>8.0.29-tidb-1.0.0</tidb-mysql.version>
        <tidb-loadbalance.version>0.0.5</tidb-loadbalance.version>
        <springdoc.version>2.0.2</springdoc.version>
        <swagger.version>2.2.7</swagger.version>
        <knife4j>4.3.0</knife4j>
        <javax.mail>1.4.7</javax.mail>
        <rhino-engine.version>1.7.14</rhino-engine.version>
        <commons-lang.version>2.6</commons-lang.version>
        <aliyun.sdk.oss.version>3.17.4</aliyun.sdk.oss.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.sdk.oss.version}</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${commons-lang.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mozilla</groupId>
                <artifactId>rhino-engine</artifactId>
                <version>${rhino-engine.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.mail</groupId>
                <artifactId>mail</artifactId>
                <version>${javax.mail}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
                <version>${knife4j}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-trace</artifactId>
                <version>${apm.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger.core.v3</groupId>
                <artifactId>swagger-annotations-jakarta</artifactId>
                <version>${swagger.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.lastincisor</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${tidb-mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.lastincisor</groupId>
                <artifactId>tidb-loadbalance</artifactId>
                <version>${tidb-loadbalance.version}</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.skywalking</groupId>
                <artifactId>apm-toolkit-logback-1.x</artifactId>
                <version>${apm.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>${jetbrains.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>deployRelease</id>
            <url>http://maven.eju-inc.com/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>deploySnapshot</id>
            <url>http://maven.eju-inc.com/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <scm>
        <connection>scm:git:http://git.eju-inc.com/ejuetc/java/commons.git</connection>
        <developerConnection>scm:git:http://git.eju-inc.com/ejuetc/java/commons.git</developerConnection>
        <tag>HEAD</tag>
    </scm>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <providerImplementations>
                        <git>jgit</git>
                    </providerImplementations>
                    <username>ejuetc2023</username>
                    <password>ejuetc@2023</password>
                    <tagBase>${project.artifactId}-${project.version}</tagBase>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-scm-plugin</artifactId>
                <version>1.8.1</version>
                <configuration>
                    <tag>${project.artifactId}-${project.version}</tag>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
        <repository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <url>http://maven.aliyun.com/nexus/content/repositories/central/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
    </repositories>
</project>
