server:
  port: 8081
spring:
  application:
    name: eureka
eureka:
  client:
    registerWithEureka: false
    fetchRegistry: false
    service-url:
      defaultZone: http://localhost:8081/eureka
  server:
    waitTimeInMsWhenSyncEmpty: 0
  instance:
    lease-renewal-interval-in-seconds: 1
    lease-expiration-duration-in-seconds: 3
management:
  endpoints:
    web:
      exposure:
        include: "*"

logging:
  config: classpath:logback-spring.xml