eureka:
  client:
    registerWithEureka: false
    fetchRegistry: false
    service-url:
      defaultZone: ${EUREKA_URL}
  server:
    waitTimeInMsWhenSyncEmpty: 0
  instance:
    hostname: ${HOST_NAME}
    lease-renewal-interval-in-seconds: 1
    lease-expiration-duration-in-seconds: 3
management:
  endpoints:
    web:
      exposure:
        include: "*"
logging:
  config: classpath:logback-spring.xml